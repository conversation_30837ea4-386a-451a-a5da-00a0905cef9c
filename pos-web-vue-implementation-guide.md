# POS-Web Vue.js 详细实施方案

**基于 POS 系统前端软件架构设计方案的 Vue.js 实施指南**

---

## 目录

- [1. 项目概述](#1-项目概述)
- [2. 技术栈与工具链](#2-技术栈与工具链)
- [3. 项目目录结构设计](#3-项目目录结构设计)
- [4. Vue.js 组件化架构设计](#4-vuejs-组件化架构设计)
- [5. 状态管理方案](#5-状态管理方案)
- [6. 路由设计规范](#6-路由设计规范)
- [7. API 接口调用规范](#7-api-接口调用规范)
- [8. 样式管理和主题系统](#8-样式管理和主题系统)
- [9. 工具函数和公共方法组织](#9-工具函数和公共方法组织)
- [10. VueUse 库集成方案](#10-vueuse-库集成方案)
- [11. 代码规范和开发约定](#11-代码规范和开发约定)
- [12. 构建和部署配置](#12-构建和部署配置)
- [13. 实施步骤规划](#13-实施步骤规划)
- [14. 工时预估与人员配置](#14-工时预估与人员配置)

---

## 1. 项目概述

### 1.1 项目背景

pos-web 是 POS 系统的前端核心部分，基于 Vue 3 + TypeScript 构建，运行在 WebView2 容器中。项目需要支持约 300 个页面（包括弹层、不同状态的页面变体），涵盖收银、进销存、会员管理、报表分析等完整的 POS 业务场景。

### 1.2 核心目标

- **高效开发**: 建立完善的组件库和开发规范，提升开发效率
- **类型安全**: 全面使用 TypeScript，确保代码质量和可维护性
- **响应式设计**: 支持多分辨率适配，提供一致的用户体验
- **模块化架构**: 清晰的代码组织结构，便于团队协作和长期维护
- **性能优化**: 合理的代码分割和懒加载策略，确保应用流畅运行

### 1.3 技术特点

- 基于 Vue 3 Composition API 的现代化开发模式
- TypeScript 全覆盖，提供完整的类型安全保障
- 与原生 C# 应用通过 JSON-RPC 2.0 协议进行通信
- 集成 TDesign 组件库，提供企业级 UI 组件
- 使用 Pinia 进行状态管理，支持状态持久化
- VueUse 工具库增强开发体验

---

## 2. 技术栈与工具链

### 2.1 核心技术栈

| 技术/库 | 版本 | 用途 | 说明 |
|---------|------|------|------|
| Vue | 3.4+ | 前端框架 | 使用 Composition API |
| TypeScript | 5.0+ | 类型系统 | 全项目类型覆盖 |
| Vite | 5.0+ | 构建工具 | 快速开发和构建 |
| Vue Router | 4.0+ | 路由管理 | SPA 路由控制 |
| Pinia | 2.1+ | 状态管理 | 替代 Vuex 的现代状态管理 |
| TDesign Vue Next | 1.9+ | UI 组件库 | 腾讯企业级组件库 |
| VueUse | 10.0+ | 工具库 | Vue 组合式函数集合 |
| Axios | 1.6+ | HTTP 客户端 | API 请求处理 |

### 2.2 开发工具链

| 工具 | 用途 | 配置要点 |
|------|------|----------|
| ESLint | 代码检查 | 基于 @vue/eslint-config-typescript |
| Prettier | 代码格式化 | 统一代码风格 |
| Stylelint | 样式检查 | SCSS/CSS 规范检查 |
| Husky | Git 钩子 | 提交前代码检查 |
| lint-staged | 暂存区检查 | 只检查变更文件 |
| Commitizen | 提交规范 | 标准化提交信息 |

### 2.3 构建和部署

```bash
# 开发环境
npm run dev

# 构建生产版本
npm run build

# 类型检查
npm run type-check

# 代码检查
npm run lint

# 代码格式化
npm run format
```

---

## 3. 项目目录结构设计

### 3.1 完整目录结构

```plaintext
/pos-web
├── public/                          # 静态资源目录
│   ├── favicon.ico                  # 应用图标
│   ├── logo.png                     # 应用 Logo
│   └── index.html                   # HTML 模板
├── src/                             # 源代码目录
│   ├── api/                         # API 接口层
│   │   ├── modules/                 # 按业务模块分组的 API
│   │   │   ├── auth.ts              # 认证相关 API
│   │   │   ├── product.ts           # 商品相关 API
│   │   │   ├── order.ts             # 订单相关 API
│   │   │   ├── member.ts            # 会员相关 API
│   │   │   ├── report.ts            # 报表相关 API
│   │   │   └── index.ts             # API 模块导出
│   │   ├── types/                   # API 类型定义
│   │   │   ├── common.ts            # 通用类型
│   │   │   ├── auth.ts              # 认证类型
│   │   │   ├── product.ts           # 商品类型
│   │   │   ├── order.ts             # 订单类型
│   │   │   └── index.ts             # 类型导出
│   │   ├── request.ts               # Axios 实例配置
│   │   └── index.ts                 # API 统一导出
│   ├── assets/                      # 静态资源
│   │   ├── fonts/                   # 字体文件
│   │   ├── icons/                   # SVG 图标
│   │   │   ├── common/              # 通用图标
│   │   │   ├── business/            # 业务图标
│   │   │   └── index.ts             # 图标导出
│   │   ├── images/                  # 图片资源
│   │   │   ├── common/              # 通用图片
│   │   │   ├── backgrounds/         # 背景图片
│   │   │   └── illustrations/       # 插画图片
│   │   └── styles/                  # 样式文件
│   │       ├── base/                # 基础样式
│   │       │   ├── reset.scss       # 样式重置
│   │       │   ├── variables.scss   # SCSS 变量
│   │       │   ├── mixins.scss      # SCSS 混入
│   │       │   └── typography.scss  # 字体排版
│   │       ├── components/          # 组件样式
│   │       ├── layouts/             # 布局样式
│   │       ├── pages/               # 页面样式
│   │       ├── themes/              # 主题样式
│   │       │   ├── default.scss     # 默认主题
│   │       │   └── dark.scss        # 深色主题
│   │       ├── responsive.scss      # 响应式样式
│   │       └── main.scss            # 主样式文件
│   ├── bridge/                      # 原生通信桥接
│   │   ├── types.ts                 # 通信类型定义
│   │   ├── service.ts               # 通信服务实现
│   │   ├── api.ts                   # 原生 API 封装
│   │   └── index.ts                 # 桥接层导出
│   ├── components/                  # 组件目录
│   │   ├── base/                    # 基础组件
│   │   │   ├── BaseButton/          # 按钮组件
│   │   │   │   ├── index.vue        # 组件实现
│   │   │   │   ├── types.ts         # 组件类型
│   │   │   │   └── index.ts         # 组件导出
│   │   │   ├── BaseInput/           # 输入框组件
│   │   │   ├── BaseModal/           # 模态框组件
│   │   │   ├── BaseTable/           # 表格组件
│   │   │   ├── BaseForm/            # 表单组件
│   │   │   └── index.ts             # 基础组件导出
│   │   ├── business/                # 业务组件
│   │   │   ├── ProductCard/         # 商品卡片
│   │   │   ├── OrderList/           # 订单列表
│   │   │   ├── MemberInfo/          # 会员信息
│   │   │   ├── PaymentPanel/        # 支付面板
│   │   │   ├── CashierKeyboard/     # 收银键盘
│   │   │   └── index.ts             # 业务组件导出
│   │   ├── layout/                  # 布局组件
│   │   │   ├── AppHeader/           # 应用头部
│   │   │   ├── AppSidebar/          # 侧边栏
│   │   │   ├── AppFooter/           # 应用底部
│   │   │   ├── PageContainer/       # 页面容器
│   │   │   └── index.ts             # 布局组件导出
│   │   └── index.ts                 # 组件统一导出
│   ├── composables/                 # 组合式函数
│   │   ├── core/                    # 核心功能
│   │   │   ├── useResponsive.ts     # 响应式布局
│   │   │   ├── useTheme.ts          # 主题切换
│   │   │   ├── usePermission.ts     # 权限控制
│   │   │   ├── useLoading.ts        # 加载状态
│   │   │   └── useEventBus.ts       # 事件总线
│   │   ├── business/                # 业务功能
│   │   │   ├── useCart.ts           # 购物车逻辑
│   │   │   ├── usePayment.ts        # 支付逻辑
│   │   │   ├── useMember.ts         # 会员逻辑
│   │   │   ├── useProduct.ts        # 商品逻辑
│   │   │   └── useOrder.ts          # 订单逻辑
│   │   ├── native/                  # 原生交互
│   │   │   ├── usePrinter.ts        # 打印功能
│   │   │   ├── useScanner.ts        # 扫码功能
│   │   │   ├── useHardware.ts       # 硬件控制
│   │   │   └── useNativeApi.ts      # 原生 API
│   │   └── index.ts                 # 组合式函数导出
│   ├── constants/                   # 常量定义
│   │   ├── api.ts                   # API 相关常量
│   │   ├── business.ts              # 业务常量
│   │   ├── ui.ts                    # UI 相关常量
│   │   ├── enums.ts                 # 枚举定义
│   │   └── index.ts                 # 常量导出
│   ├── directives/                  # 自定义指令
│   │   ├── loading.ts               # 加载指令
│   │   ├── permission.ts            # 权限指令
│   │   ├── responsive.ts            # 响应式指令
│   │   └── index.ts                 # 指令导出
│   ├── layouts/                     # 页面布局
│   │   ├── DefaultLayout.vue        # 默认布局
│   │   ├── CashierLayout.vue        # 收银布局
│   │   ├── FullscreenLayout.vue     # 全屏布局
│   │   ├── SimpleLayout.vue         # 简单布局
│   │   └── index.ts                 # 布局导出
│   ├── plugins/                     # 插件配置
│   │   ├── tdesign.ts               # TDesign 配置
│   │   ├── router.ts                # 路由配置
│   │   ├── pinia.ts                 # Pinia 配置
│   │   └── index.ts                 # 插件导出
│   ├── router/                      # 路由配置
│   │   ├── modules/                 # 路由模块
│   │   │   ├── auth.ts              # 认证路由
│   │   │   ├── cashier.ts           # 收银路由
│   │   │   ├── product.ts           # 商品路由
│   │   │   ├── order.ts             # 订单路由
│   │   │   ├── member.ts            # 会员路由
│   │   │   ├── report.ts            # 报表路由
│   │   │   └── index.ts             # 路由模块导出
│   │   ├── guards/                  # 路由守卫
│   │   │   ├── auth.ts              # 认证守卫
│   │   │   ├── permission.ts        # 权限守卫
│   │   │   └── index.ts             # 守卫导出
│   │   ├── types.ts                 # 路由类型
│   │   └── index.ts                 # 路由主配置
│   ├── stores/                      # 状态管理
│   │   ├── modules/                 # 状态模块
│   │   │   ├── auth.ts              # 认证状态
│   │   │   ├── user.ts              # 用户状态
│   │   │   ├── cart.ts              # 购物车状态
│   │   │   ├── product.ts           # 商品状态
│   │   │   ├── order.ts             # 订单状态
│   │   │   ├── member.ts            # 会员状态
│   │   │   ├── settings.ts          # 设置状态
│   │   │   └── app.ts               # 应用状态
│   │   ├── types/                   # 状态类型
│   │   │   ├── auth.ts              # 认证类型
│   │   │   ├── user.ts              # 用户类型
│   │   │   ├── cart.ts              # 购物车类型
│   │   │   └── index.ts             # 类型导出
│   │   ├── plugins/                 # 状态插件
│   │   │   ├── persist.ts           # 持久化插件
│   │   │   └── index.ts             # 插件导出
│   │   └── index.ts                 # 状态管理导出
│   ├── types/                       # 全局类型定义
│   │   ├── global.d.ts              # 全局类型声明
│   │   ├── env.d.ts                 # 环境变量类型
│   │   ├── components.d.ts          # 组件类型声明
│   │   └── index.ts                 # 类型导出
│   ├── utils/                       # 工具函数
│   │   ├── common/                  # 通用工具
│   │   │   ├── format.ts            # 格式化工具
│   │   │   ├── validate.ts          # 验证工具
│   │   │   ├── storage.ts           # 存储工具
│   │   │   ├── date.ts              # 日期工具
│   │   │   └── number.ts            # 数字工具
│   │   ├── business/                # 业务工具
│   │   │   ├── price.ts             # 价格计算
│   │   │   ├── discount.ts          # 折扣计算
│   │   │   ├── inventory.ts         # 库存计算
│   │   │   └── payment.ts           # 支付工具
│   │   ├── ui/                      # UI 工具
│   │   │   ├── responsive.ts        # 响应式工具
│   │   │   ├── theme.ts             # 主题工具
│   │   │   └── animation.ts         # 动画工具
│   │   └── index.ts                 # 工具函数导出
│   ├── views/                       # 页面组件
│   │   ├── auth/                    # 认证页面
│   │   │   ├── Login.vue            # 登录页面
│   │   │   ├── Register.vue         # 注册页面
│   │   │   └── ForgotPassword.vue   # 忘记密码
│   │   ├── dashboard/               # 仪表盘
│   │   │   ├── Overview.vue         # 概览页面
│   │   │   ├── Analytics.vue        # 分析页面
│   │   │   └── QuickActions.vue     # 快捷操作
│   │   ├── cashier/                 # 收银模块
│   │   │   ├── Main.vue             # 收银主页面
│   │   │   ├── ProductSelection.vue # 商品选择
│   │   │   ├── Cart.vue             # 购物车
│   │   │   ├── Payment.vue          # 支付页面
│   │   │   ├── Receipt.vue          # 小票页面
│   │   │   └── components/          # 收银组件
│   │   ├── product/                 # 商品管理
│   │   │   ├── List.vue             # 商品列表
│   │   │   ├── Detail.vue           # 商品详情
│   │   │   ├── Create.vue           # 创建商品
│   │   │   ├── Edit.vue             # 编辑商品
│   │   │   ├── Category.vue         # 商品分类
│   │   │   └── components/          # 商品组件
│   │   ├── order/                   # 订单管理
│   │   │   ├── List.vue             # 订单列表
│   │   │   ├── Detail.vue           # 订单详情
│   │   │   ├── Refund.vue           # 退款页面
│   │   │   └── components/          # 订单组件
│   │   ├── member/                  # 会员管理
│   │   │   ├── List.vue             # 会员列表
│   │   │   ├── Detail.vue           # 会员详情
│   │   │   ├── Create.vue           # 创建会员
│   │   │   ├── Edit.vue             # 编辑会员
│   │   │   └── components/          # 会员组件
│   │   ├── inventory/               # 库存管理
│   │   │   ├── List.vue             # 库存列表
│   │   │   ├── Adjustment.vue       # 库存调整
│   │   │   ├── Transfer.vue         # 库存调拨
│   │   │   └── components/          # 库存组件
│   │   ├── report/                  # 报表分析
│   │   │   ├── Sales.vue            # 销售报表
│   │   │   ├── Product.vue          # 商品报表
│   │   │   ├── Member.vue           # 会员报表
│   │   │   ├── Financial.vue        # 财务报表
│   │   │   └── components/          # 报表组件
│   │   ├── settings/                # 系统设置
│   │   │   ├── General.vue          # 通用设置
│   │   │   ├── Store.vue            # 门店设置
│   │   │   ├── Payment.vue          # 支付设置
│   │   │   ├── Printer.vue          # 打印设置
│   │   │   └── components/          # 设置组件
│   │   └── error/                   # 错误页面
│   │       ├── 404.vue              # 404 页面
│   │       ├── 500.vue              # 500 页面
│   │       └── Network.vue          # 网络错误
│   ├── App.vue                      # 根组件
│   └── main.ts                      # 应用入口
├── tests/                           # 测试目录
│   ├── unit/                        # 单元测试
│   ├── integration/                 # 集成测试
│   └── e2e/                         # 端到端测试
├── docs/                            # 文档目录
│   ├── api/                         # API 文档
│   ├── components/                  # 组件文档
│   └── guides/                      # 开发指南
├── .env.development                 # 开发环境变量
├── .env.production                  # 生产环境变量
├── .env.local                       # 本地环境变量
├── .eslintrc.cjs                    # ESLint 配置
├── .gitignore                       # Git 忽略文件
├── .prettierrc                      # Prettier 配置
├── commitlint.config.js             # 提交信息检查
├── package.json                     # 项目配置
├── tsconfig.json                    # TypeScript 配置
├── vite.config.ts                   # Vite 配置
└── README.md                        # 项目说明
```

### 3.2 目录设计原则

1. **按功能模块组织**: 相关功能的文件放在同一目录下
2. **层次清晰**: 目录层级不超过 4 层，保持结构清晰
3. **命名规范**: 使用 kebab-case 命名目录，PascalCase 命名组件
4. **职责单一**: 每个目录都有明确的职责和用途
5. **易于扩展**: 结构设计便于后续功能扩展和维护

---

## 4. Vue.js 组件化架构设计

### 4.1 组件分类体系

#### 4.1.1 基础组件 (Base Components)

基础组件是最底层的 UI 组件，提供通用的界面元素，不包含业务逻辑。

**设计原则:**
- 高度可复用，不依赖具体业务场景
- 提供丰富的配置选项和插槽
- 完整的 TypeScript 类型支持
- 遵循无障碍访问标准

**组件列表:**

```typescript
// src/components/base/index.ts
export { default as BaseButton } from './BaseButton'
export { default as BaseInput } from './BaseInput'
export { default as BaseSelect } from './BaseSelect'
export { default as BaseModal } from './BaseModal'
export { default as BaseTable } from './BaseTable'
export { default as BaseForm } from './BaseForm'
export { default as BaseCard } from './BaseCard'
export { default as BaseLoading } from './BaseLoading'
export { default as BaseEmpty } from './BaseEmpty'
export { default as BasePagination } from './BasePagination'
```

**示例实现 - BaseButton:**

```vue
<!-- src/components/base/BaseButton/index.vue -->
<template>
  <button
    :class="buttonClasses"
    :disabled="disabled || loading"
    :type="htmlType"
    @click="handleClick"
  >
    <BaseLoading v-if="loading" size="small" />
    <slot v-if="!loading" name="icon" />
    <span v-if="$slots.default && !loading" class="base-button__text">
      <slot />
    </span>
  </button>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { ButtonProps, ButtonEmits } from './types'
import BaseLoading from '../BaseLoading'

const props = withDefaults(defineProps<ButtonProps>(), {
  type: 'default',
  size: 'medium',
  htmlType: 'button',
  disabled: false,
  loading: false,
  block: false,
  round: false
})

const emit = defineEmits<ButtonEmits>()

const buttonClasses = computed(() => [
  'base-button',
  `base-button--${props.type}`,
  `base-button--${props.size}`,
  {
    'base-button--disabled': props.disabled,
    'base-button--loading': props.loading,
    'base-button--block': props.block,
    'base-button--round': props.round
  }
])

const handleClick = (event: MouseEvent) => {
  if (!props.disabled && !props.loading) {
    emit('click', event)
  }
}
</script>
```

```typescript
// src/components/base/BaseButton/types.ts
export interface ButtonProps {
  type?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'default'
  size?: 'small' | 'medium' | 'large'
  htmlType?: 'button' | 'submit' | 'reset'
  disabled?: boolean
  loading?: boolean
  block?: boolean
  round?: boolean
}

export interface ButtonEmits {
  click: [event: MouseEvent]
}
```

#### 4.1.2 业务组件 (Business Components)

业务组件封装特定的业务逻辑和交互模式，可在多个页面中复用。

**设计原则:**
- 封装完整的业务功能模块
- 通过 props 和 events 与外部通信
- 内部状态管理，减少外部依赖
- 提供清晰的 API 接口

**组件列表:**

```typescript
// src/components/business/index.ts
export { default as ProductCard } from './ProductCard'
export { default as ProductSelector } from './ProductSelector'
export { default as OrderList } from './OrderList'
export { default as OrderItem } from './OrderItem'
export { default as MemberInfo } from './MemberInfo'
export { default as MemberSelector } from './MemberSelector'
export { default as PaymentPanel } from './PaymentPanel'
export { default as PaymentMethod } from './PaymentMethod'
export { default as CashierKeyboard } from './CashierKeyboard'
export { default as ReceiptPreview } from './ReceiptPreview'
export { default as InventoryAdjustment } from './InventoryAdjustment'
export { default as ReportChart } from './ReportChart'
```

**示例实现 - ProductCard:**

```vue
<!-- src/components/business/ProductCard/index.vue -->
<template>
  <BaseCard
    :class="cardClasses"
    @click="handleCardClick"
  >
    <div class="product-card__image">
      <img
        :src="product.image || defaultImage"
        :alt="product.name"
        @error="handleImageError"
      />
      <div v-if="product.stock <= 0" class="product-card__out-of-stock">
        缺货
      </div>
    </div>

    <div class="product-card__content">
      <h3 class="product-card__name" :title="product.name">
        {{ product.name }}
      </h3>

      <div class="product-card__price">
        <span class="product-card__current-price">
          ¥{{ formatPrice(product.price) }}
        </span>
        <span
          v-if="product.originalPrice && product.originalPrice > product.price"
          class="product-card__original-price"
        >
          ¥{{ formatPrice(product.originalPrice) }}
        </span>
      </div>

      <div class="product-card__meta">
        <span class="product-card__category">{{ product.categoryName }}</span>
        <span class="product-card__stock">库存: {{ product.stock }}</span>
      </div>

      <div v-if="showActions" class="product-card__actions">
        <BaseButton
          size="small"
          type="primary"
          :disabled="product.stock <= 0"
          @click.stop="handleAddToCart"
        >
          加入购物车
        </BaseButton>
        <BaseButton
          size="small"
          @click.stop="handleViewDetail"
        >
          查看详情
        </BaseButton>
      </div>
    </div>
  </BaseCard>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { BaseCard, BaseButton } from '@/components/base'
import { formatPrice } from '@/utils/business/price'
import type { ProductCardProps, ProductCardEmits } from './types'
import defaultProductImage from '@/assets/images/common/default-product.png'

const props = withDefaults(defineProps<ProductCardProps>(), {
  selectable: false,
  selected: false,
  showActions: true,
  size: 'medium'
})

const emit = defineEmits<ProductCardEmits>()

const defaultImage = defaultProductImage

const cardClasses = computed(() => [
  'product-card',
  `product-card--${props.size}`,
  {
    'product-card--selectable': props.selectable,
    'product-card--selected': props.selected,
    'product-card--out-of-stock': props.product.stock <= 0
  }
])

const handleCardClick = () => {
  if (props.selectable) {
    emit('select', props.product)
  }
}

const handleAddToCart = () => {
  if (props.product.stock > 0) {
    emit('add-to-cart', props.product)
  }
}

const handleViewDetail = () => {
  emit('view-detail', props.product)
}

const handleImageError = (event: Event) => {
  const target = event.target as HTMLImageElement
  target.src = defaultImage
}
</script>
```

#### 4.1.3 布局组件 (Layout Components)

布局组件定义页面的整体结构和导航模式。

**组件列表:**

```typescript
// src/components/layout/index.ts
export { default as AppHeader } from './AppHeader'
export { default as AppSidebar } from './AppSidebar'
export { default as AppFooter } from './AppFooter'
export { default as PageContainer } from './PageContainer'
export { default as ContentArea } from './ContentArea'
export { default as NavigationMenu } from './NavigationMenu'
export { default as Breadcrumb } from './Breadcrumb'
export { default as QuickActions } from './QuickActions'
```

### 4.2 组件命名规范

#### 4.2.1 文件命名

```plaintext
# 组件目录结构
ComponentName/
├── index.vue          # 组件实现
├── types.ts           # 类型定义
├── hooks.ts           # 组合式函数 (可选)
├── constants.ts       # 组件常量 (可选)
├── index.ts           # 导出文件
└── __tests__/         # 测试文件 (可选)
    └── index.test.ts
```

#### 4.2.2 组件命名约定

```typescript
// 基础组件: Base + 功能名称
BaseButton, BaseInput, BaseModal

// 业务组件: 业务领域 + 功能名称
ProductCard, OrderList, MemberInfo

// 布局组件: App/Page + 区域名称
AppHeader, PageContainer, ContentArea

// 页面组件: 功能模块名称
CashierMain, ProductList, OrderDetail
```

### 4.3 组件间通信

#### 4.3.1 Props 和 Events

```typescript
// 标准的 Props 定义
interface ComponentProps {
  // 必需属性
  id: string
  name: string

  // 可选属性
  disabled?: boolean
  loading?: boolean

  // 联合类型
  size?: 'small' | 'medium' | 'large'

  // 对象类型
  config?: {
    autoSave: boolean
    interval: number
  }

  // 函数类型
  validator?: (value: string) => boolean
}

// 标准的 Events 定义
interface ComponentEmits {
  // 简单事件
  click: []

  // 带参数事件
  change: [value: string]
  update: [id: string, data: Record<string, any>]

  // 复杂事件
  'selection-change': [selectedItems: Array<{ id: string; name: string }>]
}
```

#### 4.3.2 Provide/Inject

```typescript
// src/composables/core/useProvideInject.ts
import { provide, inject, type InjectionKey } from 'vue'

// 定义注入键
export const FORM_CONTEXT_KEY: InjectionKey<FormContext> = Symbol('form-context')
export const TABLE_CONTEXT_KEY: InjectionKey<TableContext> = Symbol('table-context')

// 表单上下文
export interface FormContext {
  disabled: boolean
  size: 'small' | 'medium' | 'large'
  validateField: (field: string) => Promise<boolean>
  clearValidation: (field?: string) => void
}

// 提供表单上下文
export function useProvideForm(context: FormContext) {
  provide(FORM_CONTEXT_KEY, context)
}

// 注入表单上下文
export function useInjectForm() {
  const context = inject(FORM_CONTEXT_KEY)
  if (!context) {
    throw new Error('useInjectForm must be used within a form component')
  }
  return context
}
```

#### 4.3.3 事件总线

```typescript
// src/composables/core/useEventBus.ts
import { ref, type Ref } from 'vue'

type EventHandler<T = any> = (payload: T) => void
type EventMap = Record<string, EventHandler[]>

class EventBus {
  private events: EventMap = {}

  on<T = any>(event: string, handler: EventHandler<T>) {
    if (!this.events[event]) {
      this.events[event] = []
    }
    this.events[event].push(handler)
  }

  off<T = any>(event: string, handler: EventHandler<T>) {
    if (!this.events[event]) return

    const index = this.events[event].indexOf(handler)
    if (index > -1) {
      this.events[event].splice(index, 1)
    }
  }

  emit<T = any>(event: string, payload?: T) {
    if (!this.events[event]) return

    this.events[event].forEach(handler => {
      try {
        handler(payload)
      } catch (error) {
        console.error(`Error in event handler for "${event}":`, error)
      }
    })
  }

  clear() {
    this.events = {}
  }
}

const globalEventBus = new EventBus()

export function useEventBus() {
  return {
    on: globalEventBus.on.bind(globalEventBus),
    off: globalEventBus.off.bind(globalEventBus),
    emit: globalEventBus.emit.bind(globalEventBus),
    clear: globalEventBus.clear.bind(globalEventBus)
  }
}

// 业务事件类型定义
export interface BusinessEvents {
  'cart:item-added': { productId: string; quantity: number }
  'cart:item-removed': { productId: string }
  'cart:cleared': void
  'payment:completed': { orderId: string; amount: number }
  'member:selected': { memberId: string; memberInfo: any }
  'product:scanned': { barcode: string; product?: any }
}

// 类型安全的事件总线
export function useTypedEventBus<T extends Record<string, any> = BusinessEvents>() {
  return {
    on: <K extends keyof T>(event: K, handler: (payload: T[K]) => void) => {
      globalEventBus.on(event as string, handler)
    },
    off: <K extends keyof T>(event: K, handler: (payload: T[K]) => void) => {
      globalEventBus.off(event as string, handler)
    },
    emit: <K extends keyof T>(event: K, payload: T[K]) => {
      globalEventBus.emit(event as string, payload)
    }
  }
}
```

---

## 5. 状态管理方案

### 5.1 Pinia 架构设计

#### 5.1.1 Store 模块划分

```typescript
// src/stores/index.ts
import { createPinia } from 'pinia'
import { createPersistedState } from 'pinia-plugin-persistedstate'

const pinia = createPinia()

// 配置持久化插件
pinia.use(createPersistedState({
  storage: localStorage,
  key: id => `pos-web-${id}`,
  auto: true
}))

export default pinia

// 导出所有 store
export { useAuthStore } from './modules/auth'
export { useUserStore } from './modules/user'
export { useCartStore } from './modules/cart'
export { useProductStore } from './modules/product'
export { useOrderStore } from './modules/order'
export { useMemberStore } from './modules/member'
export { useSettingsStore } from './modules/settings'
export { useAppStore } from './modules/app'
```

#### 5.1.2 认证状态管理

```typescript
// src/stores/modules/auth.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { authApi } from '@/api'
import type { LoginParams, UserInfo, AuthState } from '@/stores/types/auth'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref<string>('')
  const refreshToken = ref<string>('')
  const userInfo = ref<UserInfo | null>(null)
  const permissions = ref<string[]>([])
  const isLoading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!userInfo.value)
  const hasPermission = computed(() => (permission: string) => {
    return permissions.value.includes(permission) || permissions.value.includes('*')
  })

  // 操作
  const login = async (params: LoginParams) => {
    try {
      isLoading.value = true
      const response = await authApi.login(params)

      if (response.success) {
        token.value = response.data.token
        refreshToken.value = response.data.refreshToken
        userInfo.value = response.data.userInfo
        permissions.value = response.data.permissions

        return { success: true }
      } else {
        return { success: false, message: response.message }
      }
    } catch (error) {
      console.error('Login error:', error)
      return { success: false, message: '登录失败，请重试' }
    } finally {
      isLoading.value = false
    }
  }

  const logout = async () => {
    try {
      if (token.value) {
        await authApi.logout()
      }
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      // 清除本地状态
      token.value = ''
      refreshToken.value = ''
      userInfo.value = null
      permissions.value = []
    }
  }

  const refreshAccessToken = async () => {
    try {
      if (!refreshToken.value) {
        throw new Error('No refresh token available')
      }

      const response = await authApi.refreshToken(refreshToken.value)

      if (response.success) {
        token.value = response.data.token
        return true
      } else {
        await logout()
        return false
      }
    } catch (error) {
      console.error('Token refresh error:', error)
      await logout()
      return false
    }
  }

  const updateUserInfo = (newUserInfo: Partial<UserInfo>) => {
    if (userInfo.value) {
      userInfo.value = { ...userInfo.value, ...newUserInfo }
    }
  }

  return {
    // 状态
    token,
    refreshToken,
    userInfo,
    permissions,
    isLoading,

    // 计算属性
    isAuthenticated,
    hasPermission,

    // 操作
    login,
    logout,
    refreshAccessToken,
    updateUserInfo
  }
}, {
  persist: {
    paths: ['token', 'refreshToken', 'userInfo', 'permissions']
  }
})
```

#### 5.1.3 购物车状态管理

```typescript
// src/stores/modules/cart.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useEventBus } from '@/composables/core/useEventBus'
import { calculateDiscount, calculateTax } from '@/utils/business/price'
import type { CartItem, Product, DiscountRule } from '@/stores/types/cart'

export const useCartStore = defineStore('cart', () => {
  const eventBus = useEventBus()

  // 状态
  const items = ref<CartItem[]>([])
  const selectedMemberId = ref<string>('')
  const discountRules = ref<DiscountRule[]>([])
  const couponCode = ref<string>('')
  const remark = ref<string>('')

  // 计算属性
  const itemCount = computed(() => {
    return items.value.reduce((total, item) => total + item.quantity, 0)
  })

  const subtotal = computed(() => {
    return items.value.reduce((total, item) => {
      return total + (item.price * item.quantity)
    }, 0)
  })

  const discountAmount = computed(() => {
    return calculateDiscount(items.value, discountRules.value, selectedMemberId.value)
  })

  const taxAmount = computed(() => {
    return calculateTax(subtotal.value - discountAmount.value)
  })

  const total = computed(() => {
    return subtotal.value - discountAmount.value + taxAmount.value
  })

  const isEmpty = computed(() => items.value.length === 0)

  // 操作
  const addItem = (product: Product, quantity: number = 1) => {
    const existingItem = items.value.find(item => item.productId === product.id)

    if (existingItem) {
      existingItem.quantity += quantity
    } else {
      const newItem: CartItem = {
        id: `${product.id}-${Date.now()}`,
        productId: product.id,
        name: product.name,
        price: product.price,
        originalPrice: product.originalPrice,
        quantity,
        image: product.image,
        categoryId: product.categoryId,
        categoryName: product.categoryName,
        barcode: product.barcode,
        unit: product.unit
      }
      items.value.push(newItem)
    }

    // 发送事件
    eventBus.emit('cart:item-added', { productId: product.id, quantity })
  }

  const removeItem = (itemId: string) => {
    const index = items.value.findIndex(item => item.id === itemId)
    if (index > -1) {
      const item = items.value[index]
      items.value.splice(index, 1)

      // 发送事件
      eventBus.emit('cart:item-removed', { productId: item.productId })
    }
  }

  const updateQuantity = (itemId: string, quantity: number) => {
    const item = items.value.find(item => item.id === itemId)
    if (item) {
      if (quantity <= 0) {
        removeItem(itemId)
      } else {
        item.quantity = quantity
      }
    }
  }

  const updatePrice = (itemId: string, price: number) => {
    const item = items.value.find(item => item.id === itemId)
    if (item) {
      item.price = price
    }
  }

  const clear = () => {
    items.value = []
    selectedMemberId.value = ''
    couponCode.value = ''
    remark.value = ''

    // 发送事件
    eventBus.emit('cart:cleared')
  }

  const setMember = (memberId: string) => {
    selectedMemberId.value = memberId
  }

  const applyCoupon = (code: string) => {
    couponCode.value = code
  }

  const setRemark = (text: string) => {
    remark.value = text
  }

  // 批量操作
  const addMultipleItems = (products: Array<{ product: Product; quantity: number }>) => {
    products.forEach(({ product, quantity }) => {
      addItem(product, quantity)
    })
  }

  const getItemByProductId = (productId: string) => {
    return items.value.find(item => item.productId === productId)
  }

  return {
    // 状态
    items,
    selectedMemberId,
    discountRules,
    couponCode,
    remark,

    // 计算属性
    itemCount,
    subtotal,
    discountAmount,
    taxAmount,
    total,
    isEmpty,

    // 操作
    addItem,
    removeItem,
    updateQuantity,
    updatePrice,
    clear,
    setMember,
    applyCoupon,
    setRemark,
    addMultipleItems,
    getItemByProductId
  }
}, {
  persist: {
    paths: ['items', 'selectedMemberId', 'couponCode', 'remark']
  }
})
```

### 5.2 状态持久化策略

#### 5.2.1 持久化配置

```typescript
// src/stores/plugins/persist.ts
import type { PersistenceOptions } from 'pinia-plugin-persistedstate'

// 敏感数据加密存储
export const createEncryptedStorage = (key: string) => ({
  getItem: (key: string) => {
    const item = localStorage.getItem(key)
    if (!item) return null

    try {
      // 这里可以添加解密逻辑
      return JSON.parse(item)
    } catch {
      return null
    }
  },
  setItem: (key: string, value: string) => {
    try {
      // 这里可以添加加密逻辑
      localStorage.setItem(key, value)
    } catch (error) {
      console.error('Failed to save to localStorage:', error)
    }
  },
  removeItem: (key: string) => {
    localStorage.removeItem(key)
  }
})

// 不同模块的持久化策略
export const persistConfig = {
  // 认证信息 - 加密存储
  auth: {
    storage: createEncryptedStorage('auth'),
    paths: ['token', 'refreshToken', 'userInfo', 'permissions']
  } as PersistenceOptions,

  // 购物车 - 普通存储，自动清理
  cart: {
    storage: localStorage,
    paths: ['items', 'selectedMemberId', 'couponCode', 'remark'],
    beforeRestore: (context) => {
      // 检查购物车数据是否过期（例如超过24小时）
      const timestamp = localStorage.getItem('cart-timestamp')
      if (timestamp) {
        const age = Date.now() - parseInt(timestamp)
        if (age > 24 * 60 * 60 * 1000) { // 24小时
          localStorage.removeItem('pos-web-cart')
          return false
        }
      }
      return true
    },
    afterRestore: () => {
      localStorage.setItem('cart-timestamp', Date.now().toString())
    }
  } as PersistenceOptions,

  // 应用设置 - 永久存储
  settings: {
    storage: localStorage,
    paths: ['theme', 'language', 'layout', 'preferences']
  } as PersistenceOptions,

  // 用户偏好 - 会话存储
  userPreferences: {
    storage: sessionStorage,
    paths: ['recentProducts', 'quickActions', 'viewMode']
  } as PersistenceOptions
}
```

#### 5.2.2 状态同步机制

```typescript
// src/stores/plugins/sync.ts
import { watch } from 'vue'
import type { Store } from 'pinia'

// 跨标签页状态同步
export function setupCrossTabSync(store: Store) {
  // 监听 storage 事件
  window.addEventListener('storage', (event) => {
    if (event.key?.startsWith('pos-web-')) {
      const storeId = event.key.replace('pos-web-', '')

      if (storeId === store.$id && event.newValue) {
        try {
          const newState = JSON.parse(event.newValue)
          store.$patch(newState)
        } catch (error) {
          console.error('Failed to sync state from storage:', error)
        }
      }
    }
  })
}

// 状态变更通知
export function setupStateChangeNotification(store: Store) {
  watch(
    () => store.$state,
    (newState) => {
      // 发送状态变更事件给原生层
      window.dispatchEvent(new CustomEvent('store-state-changed', {
        detail: {
          storeId: store.$id,
          state: newState
        }
      }))
    },
    { deep: true }
  )
}
```

---

## 6. 路由设计规范

### 6.1 路由架构

#### 6.1.1 路由模块化

```typescript
// src/router/index.ts
import { createRouter, createWebHistory } from 'vue-router'
import { setupRouterGuards } from './guards'

// 导入路由模块
import authRoutes from './modules/auth'
import dashboardRoutes from './modules/dashboard'
import cashierRoutes from './modules/cashier'
import productRoutes from './modules/product'
import orderRoutes from './modules/order'
import memberRoutes from './modules/member'
import inventoryRoutes from './modules/inventory'
import reportRoutes from './modules/report'
import settingsRoutes from './modules/settings'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      redirect: '/dashboard'
    },
    ...authRoutes,
    ...dashboardRoutes,
    ...cashierRoutes,
    ...productRoutes,
    ...orderRoutes,
    ...memberRoutes,
    ...inventoryRoutes,
    ...reportRoutes,
    ...settingsRoutes,
    {
      path: '/:pathMatch(.*)*',
      name: 'NotFound',
      component: () => import('@/views/error/404.vue')
    }
  ]
})

// 设置路由守卫
setupRouterGuards(router)

export default router
```

#### 6.1.2 收银模块路由

```typescript
// src/router/modules/cashier.ts
import type { RouteRecordRaw } from 'vue-router'

const cashierRoutes: RouteRecordRaw[] = [
  {
    path: '/cashier',
    name: 'Cashier',
    component: () => import('@/layouts/CashierLayout.vue'),
    meta: {
      title: '收银台',
      requiresAuth: true,
      permissions: ['cashier:access'],
      keepAlive: true
    },
    children: [
      {
        path: '',
        name: 'CashierMain',
        component: () => import('@/views/cashier/Main.vue'),
        meta: {
          title: '收银主页面',
          icon: 'cashier'
        }
      },
      {
        path: 'product-selection',
        name: 'CashierProductSelection',
        component: () => import('@/views/cashier/ProductSelection.vue'),
        meta: {
          title: '商品选择',
          icon: 'product'
        }
      },
      {
        path: 'payment',
        name: 'CashierPayment',
        component: () => import('@/views/cashier/Payment.vue'),
        meta: {
          title: '支付页面',
          icon: 'payment'
        }
      },
      {
        path: 'receipt/:orderId',
        name: 'CashierReceipt',
        component: () => import('@/views/cashier/Receipt.vue'),
        props: true,
        meta: {
          title: '小票预览',
          icon: 'receipt'
        }
      }
    ]
  }
]

export default cashierRoutes
```

#### 6.1.3 商品管理路由

```typescript
// src/router/modules/product.ts
import type { RouteRecordRaw } from 'vue-router'

const productRoutes: RouteRecordRaw[] = [
  {
    path: '/product',
    name: 'Product',
    component: () => import('@/layouts/DefaultLayout.vue'),
    meta: {
      title: '商品管理',
      requiresAuth: true,
      permissions: ['product:access']
    },
    children: [
      {
        path: '',
        name: 'ProductList',
        component: () => import('@/views/product/List.vue'),
        meta: {
          title: '商品列表',
          icon: 'list',
          keepAlive: true
        }
      },
      {
        path: 'create',
        name: 'ProductCreate',
        component: () => import('@/views/product/Create.vue'),
        meta: {
          title: '新增商品',
          icon: 'plus',
          permissions: ['product:create']
        }
      },
      {
        path: ':id/edit',
        name: 'ProductEdit',
        component: () => import('@/views/product/Edit.vue'),
        props: true,
        meta: {
          title: '编辑商品',
          icon: 'edit',
          permissions: ['product:update']
        }
      },
      {
        path: ':id',
        name: 'ProductDetail',
        component: () => import('@/views/product/Detail.vue'),
        props: true,
        meta: {
          title: '商品详情',
          icon: 'detail'
        }
      },
      {
        path: 'category',
        name: 'ProductCategory',
        component: () => import('@/views/product/Category.vue'),
        meta: {
          title: '商品分类',
          icon: 'category',
          permissions: ['product:category']
        }
      }
    ]
  }
]

export default productRoutes
```

### 6.2 路由守卫

#### 6.2.1 认证守卫

```typescript
// src/router/guards/auth.ts
import type { Router } from 'vue-router'
import { useAuthStore } from '@/stores'
import { ElMessage } from 'element-plus'

export function setupAuthGuard(router: Router) {
  router.beforeEach(async (to, from, next) => {
    const authStore = useAuthStore()

    // 检查是否需要认证
    if (to.meta.requiresAuth) {
      if (!authStore.isAuthenticated) {
        ElMessage.warning('请先登录')
        next({
          name: 'Login',
          query: { redirect: to.fullPath }
        })
        return
      }

      // 检查 token 是否即将过期
      if (authStore.token) {
        try {
          const payload = JSON.parse(atob(authStore.token.split('.')[1]))
          const exp = payload.exp * 1000
          const now = Date.now()

          // 如果 token 在 5 分钟内过期，尝试刷新
          if (exp - now < 5 * 60 * 1000) {
            const refreshed = await authStore.refreshAccessToken()
            if (!refreshed) {
              next({ name: 'Login' })
              return
            }
          }
        } catch (error) {
          console.error('Token validation error:', error)
          next({ name: 'Login' })
          return
        }
      }
    }

    next()
  })
}
```

#### 6.2.2 权限守卫

```typescript
// src/router/guards/permission.ts
import type { Router } from 'vue-router'
import { useAuthStore } from '@/stores'
import { ElMessage } from 'element-plus'

export function setupPermissionGuard(router: Router) {
  router.beforeEach((to, from, next) => {
    const authStore = useAuthStore()

    // 检查页面权限
    if (to.meta.permissions && Array.isArray(to.meta.permissions)) {
      const hasPermission = to.meta.permissions.some(permission =>
        authStore.hasPermission(permission)
      )

      if (!hasPermission) {
        ElMessage.error('您没有访问此页面的权限')
        next({ name: 'Dashboard' })
        return
      }
    }

    next()
  })
}
```

#### 6.2.3 页面标题守卫

```typescript
// src/router/guards/title.ts
import type { Router } from 'vue-router'

export function setupTitleGuard(router: Router) {
  router.afterEach((to) => {
    const title = to.meta.title as string
    if (title) {
      document.title = `${title} - POS 系统`
    } else {
      document.title = 'POS 系统'
    }
  })
}
```

#### 6.2.4 页面加载守卫

```typescript
// src/router/guards/loading.ts
import type { Router } from 'vue-router'
import { useAppStore } from '@/stores'

export function setupLoadingGuard(router: Router) {
  router.beforeEach((to, from, next) => {
    const appStore = useAppStore()

    // 显示页面加载状态
    if (to.name !== from.name) {
      appStore.setPageLoading(true)
    }

    next()
  })

  router.afterEach(() => {
    const appStore = useAppStore()

    // 隐藏页面加载状态
    setTimeout(() => {
      appStore.setPageLoading(false)
    }, 100)
  })
}
```

### 6.3 路由类型定义

```typescript
// src/router/types.ts
import type { RouteRecordRaw } from 'vue-router'

// 扩展路由元信息
declare module 'vue-router' {
  interface RouteMeta {
    // 页面标题
    title?: string

    // 页面图标
    icon?: string

    // 是否需要认证
    requiresAuth?: boolean

    // 所需权限
    permissions?: string[]

    // 是否保持组件活跃状态
    keepAlive?: boolean

    // 是否在菜单中隐藏
    hidden?: boolean

    // 面包屑路径
    breadcrumb?: Array<{
      name: string
      path?: string
    }>

    // 页面布局
    layout?: 'default' | 'cashier' | 'fullscreen' | 'simple'

    // 页面级别的加载状态
    loading?: boolean

    // 页面缓存策略
    cache?: {
      enabled: boolean
      duration?: number // 缓存时长（毫秒）
    }
  }
}

// 路由配置接口
export interface RouteConfig extends RouteRecordRaw {
  meta?: RouteMeta
  children?: RouteConfig[]
}

// 菜单项接口
export interface MenuItem {
  id: string
  name: string
  path: string
  icon?: string
  children?: MenuItem[]
  permissions?: string[]
  hidden?: boolean
  order?: number
}
```

---

## 7. API 接口调用规范

### 7.1 HTTP 客户端配置

#### 7.1.1 Axios 实例配置

```typescript
// src/api/request.ts
import axios, { type AxiosInstance, type AxiosRequestConfig, type AxiosResponse } from 'axios'
import { useAuthStore } from '@/stores'
import { ElMessage } from 'element-plus'

// 请求配置接口
interface RequestConfig extends AxiosRequestConfig {
  skipAuth?: boolean
  skipErrorHandler?: boolean
  showLoading?: boolean
  loadingText?: string
}

// 响应数据接口
interface ApiResponse<T = any> {
  success: boolean
  data: T
  message: string
  code: number
  timestamp: number
}

// 创建 axios 实例
const createAxiosInstance = (): AxiosInstance => {
  const instance = axios.create({
    baseURL: import.meta.env.VITE_API_BASE_URL,
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json'
    }
  })

  // 请求拦截器
  instance.interceptors.request.use(
    (config: RequestConfig) => {
      const authStore = useAuthStore()

      // 添加认证头
      if (!config.skipAuth && authStore.token) {
        config.headers = config.headers || {}
        config.headers.Authorization = `Bearer ${authStore.token}`
      }

      // 添加请求 ID
      config.headers = config.headers || {}
      config.headers['X-Request-ID'] = generateRequestId()

      // 添加时间戳
      config.headers['X-Timestamp'] = Date.now().toString()

      return config
    },
    (error) => {
      return Promise.reject(error)
    }
  )

  // 响应拦截器
  instance.interceptors.response.use(
    (response: AxiosResponse<ApiResponse>) => {
      const { data } = response

      // 检查业务状态码
      if (data.success) {
        return response
      } else {
        // 处理业务错误
        const error = new Error(data.message || '请求失败')
        ;(error as any).code = data.code
        ;(error as any).response = response
        return Promise.reject(error)
      }
    },
    async (error) => {
      const { response, config } = error

      if (response) {
        const { status, data } = response

        switch (status) {
          case 401:
            // 未授权，尝试刷新 token
            const authStore = useAuthStore()
            const refreshed = await authStore.refreshAccessToken()

            if (refreshed && config) {
              // 重试原请求
              return instance.request(config)
            } else {
              // 刷新失败，跳转登录
              await authStore.logout()
              window.location.href = '/login'
            }
            break

          case 403:
            ElMessage.error('没有权限访问此资源')
            break

          case 404:
            ElMessage.error('请求的资源不存在')
            break

          case 500:
            ElMessage.error('服务器内部错误')
            break

          default:
            if (!config?.skipErrorHandler) {
              ElMessage.error(data?.message || `请求失败 (${status})`)
            }
        }
      } else {
        // 网络错误
        if (!config?.skipErrorHandler) {
          ElMessage.error('网络连接失败，请检查网络设置')
        }
      }

      return Promise.reject(error)
    }
  )

  return instance
}

// 生成请求 ID
function generateRequestId(): string {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
}

// 创建实例
const request = createAxiosInstance()

// 导出请求方法
export const http = {
  get: <T = any>(url: string, config?: RequestConfig) =>
    request.get<ApiResponse<T>>(url, config).then(res => res.data),

  post: <T = any>(url: string, data?: any, config?: RequestConfig) =>
    request.post<ApiResponse<T>>(url, data, config).then(res => res.data),

  put: <T = any>(url: string, data?: any, config?: RequestConfig) =>
    request.put<ApiResponse<T>>(url, data, config).then(res => res.data),

  delete: <T = any>(url: string, config?: RequestConfig) =>
    request.delete<ApiResponse<T>>(url, config).then(res => res.data),

  patch: <T = any>(url: string, data?: any, config?: RequestConfig) =>
    request.patch<ApiResponse<T>>(url, data, config).then(res => res.data)
}

export default request
```

#### 7.1.2 API 模块化管理

```typescript
// src/api/modules/product.ts
import { http } from '../request'
import type {
  Product,
  ProductListParams,
  ProductCreateParams,
  ProductUpdateParams,
  ProductCategory,
  ProductListResponse
} from '../types/product'

export const productApi = {
  // 获取商品列表
  getList: (params: ProductListParams) =>
    http.get<ProductListResponse>('/products', { params }),

  // 获取商品详情
  getDetail: (id: string) =>
    http.get<Product>(`/products/${id}`),

  // 创建商品
  create: (data: ProductCreateParams) =>
    http.post<Product>('/products', data),

  // 更新商品
  update: (id: string, data: ProductUpdateParams) =>
    http.put<Product>(`/products/${id}`, data),

  // 删除商品
  delete: (id: string) =>
    http.delete(`/products/${id}`),

  // 批量删除商品
  batchDelete: (ids: string[]) =>
    http.post('/products/batch-delete', { ids }),

  // 获取商品分类
  getCategories: () =>
    http.get<ProductCategory[]>('/products/categories'),

  // 根据条码搜索商品
  searchByBarcode: (barcode: string) =>
    http.get<Product>(`/products/search/barcode/${barcode}`),

  // 批量导入商品
  batchImport: (file: File) => {
    const formData = new FormData()
    formData.append('file', file)
    return http.post('/products/import', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    })
  },

  // 导出商品数据
  export: (params: ProductListParams) =>
    http.get('/products/export', {
      params,
      responseType: 'blob'
    })
}
```

#### 7.1.3 API 类型定义

```typescript
// src/api/types/product.ts

// 商品基础信息
export interface Product {
  id: string
  name: string
  barcode: string
  price: number
  originalPrice?: number
  cost: number
  stock: number
  minStock: number
  unit: string
  categoryId: string
  categoryName: string
  description?: string
  image?: string
  images?: string[]
  status: 'active' | 'inactive'
  createdAt: string
  updatedAt: string
}

// 商品分类
export interface ProductCategory {
  id: string
  name: string
  parentId?: string
  level: number
  sort: number
  children?: ProductCategory[]
}

// 商品列表查询参数
export interface ProductListParams {
  page?: number
  pageSize?: number
  keyword?: string
  categoryId?: string
  status?: 'active' | 'inactive'
  priceMin?: number
  priceMax?: number
  stockMin?: number
  stockMax?: number
  sortBy?: 'name' | 'price' | 'stock' | 'createdAt'
  sortOrder?: 'asc' | 'desc'
}

// 商品列表响应
export interface ProductListResponse {
  items: Product[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// 创建商品参数
export interface ProductCreateParams {
  name: string
  barcode: string
  price: number
  originalPrice?: number
  cost: number
  stock: number
  minStock: number
  unit: string
  categoryId: string
  description?: string
  image?: string
  images?: string[]
}

// 更新商品参数
export interface ProductUpdateParams extends Partial<ProductCreateParams> {
  status?: 'active' | 'inactive'
}
```

### 7.2 请求状态管理

#### 7.2.1 加载状态管理

```typescript
// src/composables/core/useLoading.ts
import { ref, type Ref } from 'vue'

interface LoadingState {
  [key: string]: boolean
}

const globalLoadingState = ref<LoadingState>({})

export function useLoading(key?: string) {
  const loadingKey = key || 'default'

  const isLoading = computed(() => globalLoadingState.value[loadingKey] || false)

  const setLoading = (loading: boolean) => {
    globalLoadingState.value[loadingKey] = loading
  }

  const withLoading = async <T>(
    promise: Promise<T>,
    options?: {
      key?: string
      showMessage?: boolean
      errorMessage?: string
    }
  ): Promise<T> => {
    const currentKey = options?.key || loadingKey

    try {
      setLoading(true)
      const result = await promise

      if (options?.showMessage) {
        ElMessage.success('操作成功')
      }

      return result
    } catch (error) {
      if (options?.errorMessage) {
        ElMessage.error(options.errorMessage)
      }
      throw error
    } finally {
      setLoading(false)
    }
  }

  return {
    isLoading,
    setLoading,
    withLoading
  }
}

// 全局加载状态
export function useGlobalLoading() {
  const isAnyLoading = computed(() =>
    Object.values(globalLoadingState.value).some(loading => loading)
  )

  const clearAllLoading = () => {
    globalLoadingState.value = {}
  }

  return {
    isAnyLoading,
    clearAllLoading,
    loadingState: readonly(globalLoadingState)
  }
}
```

#### 7.2.2 错误处理

```typescript
// src/composables/core/useErrorHandler.ts
import { ref } from 'vue'
import { ElMessage, ElNotification } from 'element-plus'

interface ErrorInfo {
  message: string
  code?: number
  timestamp: number
  context?: any
}

const errorHistory = ref<ErrorInfo[]>([])

export function useErrorHandler() {
  const handleError = (
    error: any,
    options?: {
      showMessage?: boolean
      showNotification?: boolean
      customMessage?: string
      context?: any
    }
  ) => {
    const errorInfo: ErrorInfo = {
      message: error.message || '未知错误',
      code: error.code,
      timestamp: Date.now(),
      context: options?.context
    }

    // 记录错误历史
    errorHistory.value.unshift(errorInfo)
    if (errorHistory.value.length > 100) {
      errorHistory.value = errorHistory.value.slice(0, 100)
    }

    // 显示错误消息
    const message = options?.customMessage || errorInfo.message

    if (options?.showNotification) {
      ElNotification.error({
        title: '操作失败',
        message,
        duration: 5000
      })
    } else if (options?.showMessage !== false) {
      ElMessage.error(message)
    }

    // 上报错误到原生层
    if (window.nativeApi) {
      window.nativeApi.logger.log({
        level: 'error',
        message: errorInfo.message,
        context: {
          code: errorInfo.code,
          stack: error.stack,
          ...options?.context
        }
      })
    }
  }

  const clearErrorHistory = () => {
    errorHistory.value = []
  }

  return {
    handleError,
    errorHistory: readonly(errorHistory),
    clearErrorHistory
  }
}
```

### 7.3 API 缓存策略

#### 7.3.1 请求缓存

```typescript
// src/composables/core/useApiCache.ts
import { ref, computed } from 'vue'

interface CacheItem<T> {
  data: T
  timestamp: number
  expiry: number
}

interface CacheOptions {
  ttl?: number // 缓存时间（毫秒）
  key?: string // 缓存键
  force?: boolean // 强制刷新
}

const cache = new Map<string, CacheItem<any>>()

export function useApiCache() {
  const generateCacheKey = (url: string, params?: any): string => {
    const paramStr = params ? JSON.stringify(params) : ''
    return `${url}:${paramStr}`
  }

  const isExpired = (item: CacheItem<any>): boolean => {
    return Date.now() > item.timestamp + item.expiry
  }

  const get = <T>(key: string): T | null => {
    const item = cache.get(key)
    if (!item || isExpired(item)) {
      cache.delete(key)
      return null
    }
    return item.data
  }

  const set = <T>(key: string, data: T, ttl: number = 5 * 60 * 1000) => {
    cache.set(key, {
      data,
      timestamp: Date.now(),
      expiry: ttl
    })
  }

  const remove = (key: string) => {
    cache.delete(key)
  }

  const clear = () => {
    cache.clear()
  }

  const cachedRequest = async <T>(
    requestFn: () => Promise<T>,
    url: string,
    params?: any,
    options: CacheOptions = {}
  ): Promise<T> => {
    const cacheKey = options.key || generateCacheKey(url, params)
    const ttl = options.ttl || 5 * 60 * 1000 // 默认5分钟

    // 检查缓存
    if (!options.force) {
      const cached = get<T>(cacheKey)
      if (cached !== null) {
        return cached
      }
    }

    // 发起请求
    const data = await requestFn()

    // 存储缓存
    set(cacheKey, data, ttl)

    return data
  }

  return {
    get,
    set,
    remove,
    clear,
    cachedRequest,
    generateCacheKey
  }
}
```

#### 7.3.2 智能缓存组合式函数

```typescript
// src/composables/business/useProductApi.ts
import { ref, computed } from 'vue'
import { productApi } from '@/api'
import { useApiCache } from '@/composables/core/useApiCache'
import { useLoading } from '@/composables/core/useLoading'
import { useErrorHandler } from '@/composables/core/useErrorHandler'
import type { Product, ProductListParams } from '@/api/types/product'

export function useProductApi() {
  const { cachedRequest } = useApiCache()
  const { withLoading } = useLoading('product')
  const { handleError } = useErrorHandler()

  const products = ref<Product[]>([])
  const total = ref(0)
  const currentProduct = ref<Product | null>(null)

  // 获取商品列表
  const getProductList = async (params: ProductListParams, useCache = true) => {
    try {
      const response = await withLoading(
        cachedRequest(
          () => productApi.getList(params),
          '/products',
          params,
          {
            ttl: useCache ? 2 * 60 * 1000 : 0, // 2分钟缓存
            force: !useCache
          }
        )
      )

      products.value = response.data.items
      total.value = response.data.total

      return response
    } catch (error) {
      handleError(error, {
        customMessage: '获取商品列表失败',
        context: { params }
      })
      throw error
    }
  }

  // 获取商品详情
  const getProductDetail = async (id: string, useCache = true) => {
    try {
      const response = await withLoading(
        cachedRequest(
          () => productApi.getDetail(id),
          `/products/${id}`,
          null,
          {
            ttl: useCache ? 5 * 60 * 1000 : 0, // 5分钟缓存
            force: !useCache
          }
        ),
        { key: `product-detail-${id}` }
      )

      currentProduct.value = response.data
      return response
    } catch (error) {
      handleError(error, {
        customMessage: '获取商品详情失败',
        context: { id }
      })
      throw error
    }
  }

  // 创建商品
  const createProduct = async (data: any) => {
    try {
      const response = await withLoading(
        productApi.create(data),
        {
          key: 'product-create',
          showMessage: true
        }
      )

      // 清除相关缓存
      const { clear } = useApiCache()
      clear() // 简单粗暴地清除所有缓存，实际项目中可以更精细化

      return response
    } catch (error) {
      handleError(error, {
        customMessage: '创建商品失败',
        context: { data }
      })
      throw error
    }
  }

  // 根据条码搜索商品
  const searchByBarcode = async (barcode: string) => {
    try {
      const response = await withLoading(
        productApi.searchByBarcode(barcode),
        { key: `barcode-search-${barcode}` }
      )

      return response.data
    } catch (error) {
      // 条码搜索失败不显示错误消息，由调用方处理
      throw error
    }
  }

  return {
    // 状态
    products: readonly(products),
    total: readonly(total),
    currentProduct: readonly(currentProduct),

    // 方法
    getProductList,
    getProductDetail,
    createProduct,
    searchByBarcode
  }
}
```

---

## 8. 样式管理和主题系统

### 8.1 SCSS 架构

#### 8.1.1 样式文件组织

```scss
// src/assets/styles/main.scss - 主样式文件
@import './base/variables';
@import './base/mixins';
@import './base/reset';
@import './base/typography';

@import './themes/default';
@import './responsive';

@import './components/index';
@import './layouts/index';
@import './pages/index';
```

#### 8.1.2 SCSS 变量系统

```scss
// src/assets/styles/base/variables.scss
// ===== 颜色系统 =====
// 主色调
$primary-color: #1890ff;
$primary-color-hover: #40a9ff;
$primary-color-active: #096dd9;
$primary-color-light: #e6f7ff;

// 辅助色
$success-color: #52c41a;
$warning-color: #faad14;
$error-color: #ff4d4f;
$info-color: #1890ff;

// 中性色
$text-color-primary: #262626;
$text-color-secondary: #595959;
$text-color-disabled: #bfbfbf;
$text-color-inverse: #ffffff;

$background-color-base: #f5f5f5;
$background-color-light: #fafafa;
$background-color-white: #ffffff;

$border-color-base: #d9d9d9;
$border-color-light: #e8e8e8;
$border-color-dark: #bfbfbf;

// ===== 尺寸系统 =====
// 间距
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;
$spacing-xxl: 48px;

// 字体大小
$font-size-xs: 12px;
$font-size-sm: 14px;
$font-size-md: 16px;
$font-size-lg: 18px;
$font-size-xl: 20px;
$font-size-xxl: 24px;

// 行高
$line-height-base: 1.5;
$line-height-sm: 1.2;
$line-height-lg: 1.8;

// 圆角
$border-radius-sm: 2px;
$border-radius-md: 4px;
$border-radius-lg: 8px;
$border-radius-xl: 12px;

// 阴影
$box-shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.03);
$box-shadow-md: 0 1px 6px rgba(0, 0, 0, 0.1);
$box-shadow-lg: 0 4px 12px rgba(0, 0, 0, 0.15);

// ===== 响应式断点 =====
$breakpoint-xs: 480px;
$breakpoint-sm: 768px;
$breakpoint-md: 1024px;
$breakpoint-lg: 1366px;
$breakpoint-xl: 1920px;

// ===== 组件尺寸 =====
// 按钮
$button-height-sm: 24px;
$button-height-md: 32px;
$button-height-lg: 40px;

// 输入框
$input-height-sm: 24px;
$input-height-md: 32px;
$input-height-lg: 40px;

// 表格
$table-row-height: 48px;
$table-header-height: 56px;

// ===== Z-index 层级 =====
$z-index-dropdown: 1000;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070;
$z-index-notification: 1080;
```

#### 8.1.3 SCSS 混入

```scss
// src/assets/styles/base/mixins.scss

// ===== 响应式混入 =====
@mixin respond-to($breakpoint) {
  @if $breakpoint == xs {
    @media (max-width: #{$breakpoint-xs - 1px}) { @content; }
  }
  @if $breakpoint == sm {
    @media (min-width: #{$breakpoint-xs}) and (max-width: #{$breakpoint-sm - 1px}) { @content; }
  }
  @if $breakpoint == md {
    @media (min-width: #{$breakpoint-sm}) and (max-width: #{$breakpoint-md - 1px}) { @content; }
  }
  @if $breakpoint == lg {
    @media (min-width: #{$breakpoint-md}) and (max-width: #{$breakpoint-lg - 1px}) { @content; }
  }
  @if $breakpoint == xl {
    @media (min-width: #{$breakpoint-lg}) { @content; }
  }
}

// ===== 布局混入 =====
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin flex-column {
  display: flex;
  flex-direction: column;
}

// ===== 文本混入 =====
@mixin text-ellipsis($lines: 1) {
  @if $lines == 1 {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  } @else {
    display: -webkit-box;
    -webkit-line-clamp: $lines;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

@mixin text-gradient($start-color, $end-color, $direction: to right) {
  background: linear-gradient($direction, $start-color, $end-color);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

// ===== 动画混入 =====
@mixin transition($properties: all, $duration: 0.3s, $timing: ease) {
  transition: $properties $duration $timing;
}

@mixin animation($name, $duration: 1s, $timing: ease, $delay: 0s, $iteration: 1, $direction: normal, $fill-mode: both) {
  animation: $name $duration $timing $delay $iteration $direction $fill-mode;
}

// ===== 按钮混入 =====
@mixin button-variant($color, $background, $border) {
  color: $color;
  background-color: $background;
  border-color: $border;

  &:hover {
    color: $color;
    background-color: lighten($background, 5%);
    border-color: lighten($border, 5%);
  }

  &:active {
    color: $color;
    background-color: darken($background, 5%);
    border-color: darken($border, 5%);
  }

  &:disabled {
    color: $text-color-disabled;
    background-color: $background-color-base;
    border-color: $border-color-base;
    cursor: not-allowed;
  }
}

// ===== 卡片混入 =====
@mixin card-shadow($level: 1) {
  @if $level == 1 {
    box-shadow: $box-shadow-sm;
  } @else if $level == 2 {
    box-shadow: $box-shadow-md;
  } @else if $level == 3 {
    box-shadow: $box-shadow-lg;
  }
}

// ===== 滚动条混入 =====
@mixin scrollbar($width: 8px, $track-color: #f1f1f1, $thumb-color: #c1c1c1) {
  &::-webkit-scrollbar {
    width: $width;
    height: $width;
  }

  &::-webkit-scrollbar-track {
    background: $track-color;
    border-radius: $width / 2;
  }

  &::-webkit-scrollbar-thumb {
    background: $thumb-color;
    border-radius: $width / 2;

    &:hover {
      background: darken($thumb-color, 10%);
    }
  }
}
```

### 8.2 主题系统

#### 8.2.1 主题配置

```typescript
// src/composables/core/useTheme.ts
import { ref, computed, watch } from 'vue'
import { useStorage } from '@vueuse/core'

export interface ThemeConfig {
  name: string
  displayName: string
  colors: {
    primary: string
    success: string
    warning: string
    error: string
    info: string
  }
  mode: 'light' | 'dark'
}

const themes: Record<string, ThemeConfig> = {
  default: {
    name: 'default',
    displayName: '默认主题',
    colors: {
      primary: '#1890ff',
      success: '#52c41a',
      warning: '#faad14',
      error: '#ff4d4f',
      info: '#1890ff'
    },
    mode: 'light'
  },
  dark: {
    name: 'dark',
    displayName: '深色主题',
    colors: {
      primary: '#177ddc',
      success: '#49aa19',
      warning: '#d89614',
      error: '#dc4446',
      info: '#177ddc'
    },
    mode: 'dark'
  },
  blue: {
    name: 'blue',
    displayName: '蓝色主题',
    colors: {
      primary: '#2f54eb',
      success: '#52c41a',
      warning: '#faad14',
      error: '#ff4d4f',
      info: '#2f54eb'
    },
    mode: 'light'
  }
}

const currentTheme = useStorage('pos-theme', 'default')

export function useTheme() {
  const theme = computed(() => themes[currentTheme.value] || themes.default)

  const setTheme = (themeName: string) => {
    if (themes[themeName]) {
      currentTheme.value = themeName
    }
  }

  const toggleDarkMode = () => {
    const isDark = theme.value.mode === 'dark'
    setTheme(isDark ? 'default' : 'dark')
  }

  // 应用主题到 CSS 变量
  const applyTheme = (themeConfig: ThemeConfig) => {
    const root = document.documentElement

    // 设置颜色变量
    Object.entries(themeConfig.colors).forEach(([key, value]) => {
      root.style.setProperty(`--color-${key}`, value)
    })

    // 设置主题模式
    root.setAttribute('data-theme', themeConfig.name)
    root.setAttribute('data-mode', themeConfig.mode)

    // 设置 TDesign 主题
    if (themeConfig.mode === 'dark') {
      root.classList.add('t-dark-theme')
    } else {
      root.classList.remove('t-dark-theme')
    }
  }

  // 监听主题变化
  watch(theme, applyTheme, { immediate: true })

  return {
    theme,
    themes: Object.values(themes),
    currentTheme,
    setTheme,
    toggleDarkMode,
    applyTheme
  }
}
```

#### 8.2.2 主题样式

```scss
// src/assets/styles/themes/default.scss
:root {
  // 使用 CSS 变量实现主题切换
  --color-primary: #{$primary-color};
  --color-success: #{$success-color};
  --color-warning: #{$warning-color};
  --color-error: #{$error-color};
  --color-info: #{$info-color};

  --text-color-primary: #{$text-color-primary};
  --text-color-secondary: #{$text-color-secondary};
  --text-color-disabled: #{$text-color-disabled};

  --background-color-base: #{$background-color-base};
  --background-color-light: #{$background-color-light};
  --background-color-white: #{$background-color-white};

  --border-color-base: #{$border-color-base};
  --border-color-light: #{$border-color-light};
  --border-color-dark: #{$border-color-dark};
}

// 深色主题
[data-mode="dark"] {
  --text-color-primary: #ffffff;
  --text-color-secondary: #d9d9d9;
  --text-color-disabled: #595959;

  --background-color-base: #141414;
  --background-color-light: #1f1f1f;
  --background-color-white: #262626;

  --border-color-base: #434343;
  --border-color-light: #303030;
  --border-color-dark: #595959;
}

// 主题特定样式
[data-theme="blue"] {
  --color-primary: #2f54eb;
  --color-info: #2f54eb;
}
```

### 8.3 响应式设计

#### 8.3.1 响应式工具类

```scss
// src/assets/styles/responsive.scss

// ===== 响应式容器 =====
.responsive-container {
  width: 100%;
  margin: 0 auto;
  padding: 0 $spacing-md;

  @include respond-to(sm) {
    max-width: 720px;
  }

  @include respond-to(md) {
    max-width: 960px;
  }

  @include respond-to(lg) {
    max-width: 1200px;
    padding: 0 $spacing-lg;
  }

  @include respond-to(xl) {
    max-width: 1600px;
    padding: 0 $spacing-xl;
  }
}

// ===== 响应式网格 =====
.responsive-grid {
  display: grid;
  gap: $spacing-md;

  // 默认单列
  grid-template-columns: 1fr;

  @include respond-to(sm) {
    grid-template-columns: repeat(2, 1fr);
  }

  @include respond-to(md) {
    grid-template-columns: repeat(3, 1fr);
  }

  @include respond-to(lg) {
    grid-template-columns: repeat(4, 1fr);
    gap: $spacing-lg;
  }

  @include respond-to(xl) {
    grid-template-columns: repeat(6, 1fr);
    gap: $spacing-xl;
  }
}

// ===== 响应式文字 =====
.responsive-text {
  font-size: $font-size-sm;

  @include respond-to(md) {
    font-size: $font-size-md;
  }

  @include respond-to(lg) {
    font-size: $font-size-lg;
  }
}

// ===== 响应式间距 =====
.responsive-spacing {
  padding: $spacing-sm;

  @include respond-to(md) {
    padding: $spacing-md;
  }

  @include respond-to(lg) {
    padding: $spacing-lg;
  }
}

// ===== 显示/隐藏工具类 =====
.hidden-xs {
  @include respond-to(xs) {
    display: none !important;
  }
}

.hidden-sm {
  @include respond-to(sm) {
    display: none !important;
  }
}

.hidden-md {
  @include respond-to(md) {
    display: none !important;
  }
}

.visible-lg-up {
  display: none !important;

  @include respond-to(lg) {
    display: block !important;
  }

  @include respond-to(xl) {
    display: block !important;
  }
}
```

#### 8.3.2 动态缩放系统

```typescript
// src/composables/core/useResponsive.ts
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useEventListener } from '@vueuse/core'

export function useResponsive() {
  const screenWidth = ref(window.innerWidth)
  const screenHeight = ref(window.innerHeight)

  // 基准尺寸（设计稿尺寸）
  const baseWidth = 1920
  const baseHeight = 1080

  // 计算缩放比例
  const scale = computed(() => {
    const scaleX = screenWidth.value / baseWidth
    const scaleY = screenHeight.value / baseHeight
    return Math.min(scaleX, scaleY, 1) // 最大不超过1
  })

  // 屏幕类型
  const screenType = computed(() => {
    const width = screenWidth.value
    if (width < 768) return 'mobile'
    if (width < 1024) return 'tablet'
    if (width < 1366) return 'laptop'
    if (width < 1920) return 'desktop'
    return 'large'
  })

  // 是否为小屏幕
  const isSmallScreen = computed(() => screenWidth.value < 1024)

  // 是否为触摸设备
  const isTouchDevice = computed(() => 'ontouchstart' in window)

  // 更新屏幕尺寸
  const updateScreenSize = () => {
    screenWidth.value = window.innerWidth
    screenHeight.value = window.innerHeight
  }

  // 应用缩放
  const applyScale = () => {
    const root = document.documentElement
    root.style.setProperty('--scale-factor', scale.value.toString())
    root.style.setProperty('--screen-width', `${screenWidth.value}px`)
    root.style.setProperty('--screen-height', `${screenHeight.value}px`)
    root.setAttribute('data-screen-type', screenType.value)
  }

  // 获取响应式尺寸
  const getResponsiveSize = (baseSize: number): number => {
    return Math.round(baseSize * scale.value)
  }

  // 获取响应式样式对象
  const getResponsiveStyle = (styles: Record<string, number>) => {
    const result: Record<string, string> = {}
    Object.entries(styles).forEach(([key, value]) => {
      result[key] = `${getResponsiveSize(value)}px`
    })
    return result
  }

  // 监听窗口大小变化
  useEventListener('resize', () => {
    updateScreenSize()
    applyScale()
  })

  onMounted(() => {
    updateScreenSize()
    applyScale()
  })

  return {
    screenWidth: readonly(screenWidth),
    screenHeight: readonly(screenHeight),
    scale: readonly(scale),
    screenType: readonly(screenType),
    isSmallScreen: readonly(isSmallScreen),
    isTouchDevice: readonly(isTouchDevice),
    getResponsiveSize,
    getResponsiveStyle
  }
}
```

---

## 9. 工具函数和公共方法组织

### 9.1 通用工具函数

#### 9.1.1 格式化工具

```typescript
// src/utils/common/format.ts

/**
 * 格式化价格
 * @param price 价格
 * @param currency 货币符号
 * @param precision 小数位数
 */
export function formatPrice(
  price: number | string,
  currency = '¥',
  precision = 2
): string {
  const num = typeof price === 'string' ? parseFloat(price) : price
  if (isNaN(num)) return `${currency}0.00`

  return `${currency}${num.toFixed(precision)}`
}

/**
 * 格式化数字，添加千分位分隔符
 * @param num 数字
 * @param precision 小数位数
 */
export function formatNumber(num: number | string, precision?: number): string {
  const number = typeof num === 'string' ? parseFloat(num) : num
  if (isNaN(number)) return '0'

  const formatted = precision !== undefined ? number.toFixed(precision) : number.toString()
  return formatted.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

/**
 * 格式化百分比
 * @param value 数值
 * @param precision 小数位数
 */
export function formatPercent(value: number, precision = 1): string {
  return `${(value * 100).toFixed(precision)}%`
}

/**
 * 格式化文件大小
 * @param bytes 字节数
 * @param precision 小数位数
 */
export function formatFileSize(bytes: number, precision = 1): string {
  if (bytes === 0) return '0 B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return `${(bytes / Math.pow(k, i)).toFixed(precision)} ${sizes[i]}`
}

/**
 * 格式化手机号
 * @param phone 手机号
 */
export function formatPhone(phone: string): string {
  const cleaned = phone.replace(/\D/g, '')
  if (cleaned.length === 11) {
    return cleaned.replace(/(\d{3})(\d{4})(\d{4})/, '$1 $2 $3')
  }
  return phone
}

/**
 * 脱敏手机号
 * @param phone 手机号
 */
export function maskPhone(phone: string): string {
  if (phone.length === 11) {
    return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
  }
  return phone
}

/**
 * 脱敏身份证号
 * @param idCard 身份证号
 */
export function maskIdCard(idCard: string): string {
  if (idCard.length === 18) {
    return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
  }
  return idCard
}
```

#### 9.1.2 验证工具

```typescript
// src/utils/common/validate.ts

/**
 * 验证手机号
 * @param phone 手机号
 */
export function isValidPhone(phone: string): boolean {
  const phoneRegex = /^1[3-9]\d{9}$/
  return phoneRegex.test(phone)
}

/**
 * 验证邮箱
 * @param email 邮箱
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * 验证身份证号
 * @param idCard 身份证号
 */
export function isValidIdCard(idCard: string): boolean {
  const idCardRegex = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/
  return idCardRegex.test(idCard)
}

/**
 * 验证密码强度
 * @param password 密码
 * @returns 强度等级 0-4
 */
export function getPasswordStrength(password: string): number {
  let strength = 0

  // 长度检查
  if (password.length >= 8) strength++
  if (password.length >= 12) strength++

  // 字符类型检查
  if (/[a-z]/.test(password)) strength++
  if (/[A-Z]/.test(password)) strength++
  if (/\d/.test(password)) strength++
  if (/[^a-zA-Z\d]/.test(password)) strength++

  return Math.min(strength, 4)
}

/**
 * 验证商品条码
 * @param barcode 条码
 */
export function isValidBarcode(barcode: string): boolean {
  // EAN-13 条码验证
  if (barcode.length === 13 && /^\d+$/.test(barcode)) {
    const checkDigit = parseInt(barcode[12])
    const sum = barcode
      .slice(0, 12)
      .split('')
      .reduce((acc, digit, index) => {
        const weight = index % 2 === 0 ? 1 : 3
        return acc + parseInt(digit) * weight
      }, 0)

    const calculatedCheckDigit = (10 - (sum % 10)) % 10
    return checkDigit === calculatedCheckDigit
  }

  // 其他格式的条码（简单验证）
  return /^[0-9A-Za-z\-_]+$/.test(barcode) && barcode.length >= 6
}

/**
 * 验证价格
 * @param price 价格
 * @param min 最小值
 * @param max 最大值
 */
export function isValidPrice(price: number, min = 0, max = 999999): boolean {
  return !isNaN(price) && price >= min && price <= max
}

/**
 * 验证库存数量
 * @param stock 库存
 */
export function isValidStock(stock: number): boolean {
  return Number.isInteger(stock) && stock >= 0
}
```

#### 9.1.3 日期工具

```typescript
// src/utils/common/date.ts
import { format, parseISO, isValid, differenceInDays, startOfDay, endOfDay } from 'date-fns'
import { zhCN } from 'date-fns/locale'

/**
 * 格式化日期
 * @param date 日期
 * @param pattern 格式模式
 */
export function formatDate(date: Date | string | number, pattern = 'yyyy-MM-dd'): string {
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : new Date(date)
    if (!isValid(dateObj)) return ''

    return format(dateObj, pattern, { locale: zhCN })
  } catch {
    return ''
  }
}

/**
 * 格式化日期时间
 * @param date 日期
 */
export function formatDateTime(date: Date | string | number): string {
  return formatDate(date, 'yyyy-MM-dd HH:mm:ss')
}

/**
 * 格式化时间
 * @param date 日期
 */
export function formatTime(date: Date | string | number): string {
  return formatDate(date, 'HH:mm:ss')
}

/**
 * 相对时间格式化
 * @param date 日期
 */
export function formatRelativeTime(date: Date | string | number): string {
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : new Date(date)
    if (!isValid(dateObj)) return ''

    const now = new Date()
    const diffInDays = differenceInDays(now, dateObj)

    if (diffInDays === 0) {
      return '今天'
    } else if (diffInDays === 1) {
      return '昨天'
    } else if (diffInDays === 2) {
      return '前天'
    } else if (diffInDays < 7) {
      return `${diffInDays}天前`
    } else {
      return formatDate(dateObj)
    }
  } catch {
    return ''
  }
}

/**
 * 获取日期范围
 * @param type 范围类型
 */
export function getDateRange(type: 'today' | 'yesterday' | 'week' | 'month' | 'quarter' | 'year'): [Date, Date] {
  const now = new Date()
  const today = startOfDay(now)

  switch (type) {
    case 'today':
      return [today, endOfDay(now)]

    case 'yesterday':
      const yesterday = new Date(today)
      yesterday.setDate(yesterday.getDate() - 1)
      return [yesterday, endOfDay(yesterday)]

    case 'week':
      const weekStart = new Date(today)
      weekStart.setDate(weekStart.getDate() - weekStart.getDay())
      const weekEnd = new Date(weekStart)
      weekEnd.setDate(weekEnd.getDate() + 6)
      return [weekStart, endOfDay(weekEnd)]

    case 'month':
      const monthStart = new Date(now.getFullYear(), now.getMonth(), 1)
      const monthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0)
      return [monthStart, endOfDay(monthEnd)]

    case 'quarter':
      const quarterStart = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3, 1)
      const quarterEnd = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3 + 3, 0)
      return [quarterStart, endOfDay(quarterEnd)]

    case 'year':
      const yearStart = new Date(now.getFullYear(), 0, 1)
      const yearEnd = new Date(now.getFullYear(), 11, 31)
      return [yearStart, endOfDay(yearEnd)]

    default:
      return [today, endOfDay(now)]
  }
}

/**
 * 检查是否为工作日
 * @param date 日期
 */
export function isWorkday(date: Date | string | number): boolean {
  const dateObj = typeof date === 'string' ? parseISO(date) : new Date(date)
  const dayOfWeek = dateObj.getDay()
  return dayOfWeek >= 1 && dayOfWeek <= 5
}
```

### 9.2 业务工具函数

#### 9.2.1 价格计算工具

```typescript
// src/utils/business/price.ts

/**
 * 计算商品总价
 * @param items 商品项目
 */
export function calculateSubtotal(items: Array<{ price: number; quantity: number }>): number {
  return items.reduce((total, item) => total + (item.price * item.quantity), 0)
}

/**
 * 计算折扣金额
 * @param subtotal 小计
 * @param discountType 折扣类型
 * @param discountValue 折扣值
 */
export function calculateDiscount(
  subtotal: number,
  discountType: 'percent' | 'amount',
  discountValue: number
): number {
  if (discountType === 'percent') {
    return subtotal * (discountValue / 100)
  } else {
    return Math.min(discountValue, subtotal)
  }
}

/**
 * 计算税费
 * @param amount 金额
 * @param taxRate 税率
 */
export function calculateTax(amount: number, taxRate = 0.06): number {
  return amount * taxRate
}

/**
 * 计算会员折扣
 * @param subtotal 小计
 * @param memberLevel 会员等级
 */
export function calculateMemberDiscount(
  subtotal: number,
  memberLevel: 'bronze' | 'silver' | 'gold' | 'platinum'
): number {
  const discountRates = {
    bronze: 0.05,   // 5% 折扣
    silver: 0.08,   // 8% 折扣
    gold: 0.12,     // 12% 折扣
    platinum: 0.15  // 15% 折扣
  }

  return subtotal * discountRates[memberLevel]
}

/**
 * 计算找零
 * @param total 总金额
 * @param paid 支付金额
 */
export function calculateChange(total: number, paid: number): number {
  return Math.max(0, paid - total)
}

/**
 * 分摊金额到各个项目
 * @param items 项目列表
 * @param totalAmount 总金额
 */
export function distributeAmount(
  items: Array<{ price: number; quantity: number }>,
  totalAmount: number
): number[] {
  const subtotal = calculateSubtotal(items)
  if (subtotal === 0) return items.map(() => 0)

  const distributed: number[] = []
  let remainingAmount = totalAmount

  for (let i = 0; i < items.length; i++) {
    const item = items[i]
    const itemTotal = item.price * item.quantity

    if (i === items.length - 1) {
      // 最后一项分配剩余金额，避免精度问题
      distributed.push(remainingAmount)
    } else {
      const itemAmount = (itemTotal / subtotal) * totalAmount
      const roundedAmount = Math.round(itemAmount * 100) / 100
      distributed.push(roundedAmount)
      remainingAmount -= roundedAmount
    }
  }

  return distributed
}

/**
 * 价格精度处理
 * @param price 价格
 * @param precision 精度
 */
export function roundPrice(price: number, precision = 2): number {
  return Math.round(price * Math.pow(10, precision)) / Math.pow(10, precision)
}
```

#### 9.2.2 库存计算工具

```typescript
// src/utils/business/inventory.ts

/**
 * 计算库存状态
 * @param currentStock 当前库存
 * @param minStock 最小库存
 * @param maxStock 最大库存
 */
export function getStockStatus(
  currentStock: number,
  minStock: number,
  maxStock?: number
): 'sufficient' | 'low' | 'out' | 'excess' {
  if (currentStock <= 0) {
    return 'out'
  } else if (currentStock <= minStock) {
    return 'low'
  } else if (maxStock && currentStock > maxStock) {
    return 'excess'
  } else {
    return 'sufficient'
  }
}

/**
 * 计算库存周转率
 * @param soldQuantity 销售数量
 * @param averageStock 平均库存
 * @param days 天数
 */
export function calculateTurnoverRate(
  soldQuantity: number,
  averageStock: number,
  days: number
): number {
  if (averageStock === 0) return 0
  return (soldQuantity / averageStock) * (365 / days)
}

/**
 * 计算安全库存
 * @param dailyUsage 日均用量
 * @param leadTime 补货周期（天）
 * @param safetyFactor 安全系数
 */
export function calculateSafetyStock(
  dailyUsage: number,
  leadTime: number,
  safetyFactor = 1.5
): number {
  return Math.ceil(dailyUsage * leadTime * safetyFactor)
}

/**
 * 计算建议补货数量
 * @param currentStock 当前库存
 * @param minStock 最小库存
 * @param maxStock 最大库存
 * @param dailyUsage 日均用量
 */
export function calculateReorderQuantity(
  currentStock: number,
  minStock: number,
  maxStock: number,
  dailyUsage: number
): number {
  if (currentStock > minStock) return 0

  const targetStock = Math.min(maxStock, minStock + dailyUsage * 7) // 目标库存为一周用量
  return Math.max(0, targetStock - currentStock)
}

/**
 * 批量计算库存价值
 * @param items 库存项目
 */
export function calculateInventoryValue(
  items: Array<{ stock: number; cost: number }>
): number {
  return items.reduce((total, item) => total + (item.stock * item.cost), 0)
}
```

### 9.3 UI 工具函数

#### 9.3.1 动画工具

```typescript
// src/utils/ui/animation.ts

/**
 * 缓动函数
 */
export const easing = {
  linear: (t: number) => t,
  easeInQuad: (t: number) => t * t,
  easeOutQuad: (t: number) => t * (2 - t),
  easeInOutQuad: (t: number) => t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t,
  easeInCubic: (t: number) => t * t * t,
  easeOutCubic: (t: number) => (--t) * t * t + 1,
  easeInOutCubic: (t: number) => t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1
}

/**
 * 数值动画
 * @param from 起始值
 * @param to 结束值
 * @param duration 持续时间
 * @param onUpdate 更新回调
 * @param easingFn 缓动函数
 */
export function animateNumber(
  from: number,
  to: number,
  duration: number,
  onUpdate: (value: number) => void,
  easingFn = easing.easeOutQuad
): () => void {
  const startTime = Date.now()
  const difference = to - from

  let animationId: number

  const animate = () => {
    const elapsed = Date.now() - startTime
    const progress = Math.min(elapsed / duration, 1)
    const easedProgress = easingFn(progress)
    const currentValue = from + difference * easedProgress

    onUpdate(currentValue)

    if (progress < 1) {
      animationId = requestAnimationFrame(animate)
    }
  }

  animationId = requestAnimationFrame(animate)

  // 返回取消函数
  return () => {
    if (animationId) {
      cancelAnimationFrame(animationId)
    }
  }
}

/**
 * 滚动到指定元素
 * @param element 目标元素
 * @param duration 持续时间
 * @param offset 偏移量
 */
export function scrollToElement(
  element: HTMLElement,
  duration = 500,
  offset = 0
): Promise<void> {
  return new Promise((resolve) => {
    const startPosition = window.pageYOffset
    const targetPosition = element.offsetTop - offset
    const distance = targetPosition - startPosition

    let startTime: number

    const animation = (currentTime: number) => {
      if (!startTime) startTime = currentTime

      const elapsed = currentTime - startTime
      const progress = Math.min(elapsed / duration, 1)
      const easedProgress = easing.easeInOutQuad(progress)

      window.scrollTo(0, startPosition + distance * easedProgress)

      if (progress < 1) {
        requestAnimationFrame(animation)
      } else {
        resolve()
      }
    }

    requestAnimationFrame(animation)
  })
}

/**
 * 元素淡入动画
 * @param element 元素
 * @param duration 持续时间
 */
export function fadeIn(element: HTMLElement, duration = 300): Promise<void> {
  return new Promise((resolve) => {
    element.style.opacity = '0'
    element.style.display = 'block'

    let startTime: number

    const animation = (currentTime: number) => {
      if (!startTime) startTime = currentTime

      const elapsed = currentTime - startTime
      const progress = Math.min(elapsed / duration, 1)

      element.style.opacity = progress.toString()

      if (progress < 1) {
        requestAnimationFrame(animation)
      } else {
        resolve()
      }
    }

    requestAnimationFrame(animation)
  })
}

/**
 * 元素淡出动画
 * @param element 元素
 * @param duration 持续时间
 */
export function fadeOut(element: HTMLElement, duration = 300): Promise<void> {
  return new Promise((resolve) => {
    const startOpacity = parseFloat(getComputedStyle(element).opacity)
    let startTime: number

    const animation = (currentTime: number) => {
      if (!startTime) startTime = currentTime

      const elapsed = currentTime - startTime
      const progress = Math.min(elapsed / duration, 1)

      element.style.opacity = (startOpacity * (1 - progress)).toString()

      if (progress < 1) {
        requestAnimationFrame(animation)
      } else {
        element.style.display = 'none'
        resolve()
      }
    }

    requestAnimationFrame(animation)
  })
}
```

---

## 10. VueUse 库集成方案

### 10.1 VueUse 安装和配置

#### 10.1.1 安装依赖

```bash
# 安装 VueUse 核心库
npm install @vueuse/core

# 安装额外的工具包（可选）
npm install @vueuse/components
npm install @vueuse/integrations
npm install @vueuse/router
```

#### 10.1.2 全局配置

```typescript
// src/plugins/vueuse.ts
import { createApp } from 'vue'
import { MotionPlugin } from '@vueuse/motion'

export function setupVueUse(app: ReturnType<typeof createApp>) {
  // 安装动画插件
  app.use(MotionPlugin)
}
```

### 10.2 推荐使用的 VueUse 功能模块

#### 10.2.1 状态管理相关

```typescript
// src/composables/vueuse/useStorageState.ts
import { useStorage, useSessionStorage, useLocalStorage } from '@vueuse/core'
import { ref, computed } from 'vue'

/**
 * 持久化状态管理
 */
export function useStorageState() {
  // 用户偏好设置
  const userPreferences = useLocalStorage('user-preferences', {
    theme: 'default',
    language: 'zh-CN',
    autoSave: true,
    notifications: true
  })

  // 会话状态
  const sessionState = useSessionStorage('session-state', {
    lastVisitedPage: '',
    searchHistory: [] as string[],
    tempData: {}
  })

  // 应用配置
  const appConfig = useStorage('app-config', {
    apiBaseUrl: '',
    timeout: 30000,
    retryCount: 3
  }, localStorage, {
    serializer: {
      read: (value: string) => {
        try {
          return JSON.parse(value)
        } catch {
          return {}
        }
      },
      write: (value: any) => JSON.stringify(value)
    }
  })

  return {
    userPreferences,
    sessionState,
    appConfig
  }
}
```

#### 10.2.2 网络状态监控

```typescript
// src/composables/vueuse/useNetworkStatus.ts
import { useOnline, useNetwork } from '@vueuse/core'
import { computed, watch } from 'vue'
import { ElMessage } from 'element-plus'

export function useNetworkStatus() {
  const isOnline = useOnline()
  const network = useNetwork()

  // 网络状态描述
  const networkStatus = computed(() => {
    if (!isOnline.value) {
      return { status: 'offline', message: '网络连接已断开' }
    }

    if (network.effectiveType.value) {
      const speedMap = {
        'slow-2g': '网络较慢',
        '2g': '网络较慢',
        '3g': '网络一般',
        '4g': '网络良好'
      }

      return {
        status: 'online',
        message: speedMap[network.effectiveType.value as keyof typeof speedMap] || '网络正常'
      }
    }

    return { status: 'online', message: '网络正常' }
  })

  // 监听网络状态变化
  watch(isOnline, (online) => {
    if (online) {
      ElMessage.success('网络连接已恢复')
    } else {
      ElMessage.error('网络连接已断开，请检查网络设置')
    }
  })

  return {
    isOnline,
    network,
    networkStatus
  }
}
```

#### 10.2.3 设备信息检测

```typescript
// src/composables/vueuse/useDeviceInfo.ts
import {
  useDevicesList,
  usePermission,
  useBattery,
  useDeviceMotion,
  useDeviceOrientation
} from '@vueuse/core'
import { computed } from 'vue'

export function useDeviceInfo() {
  const { devices } = useDevicesList()
  const cameraPermission = usePermission('camera')
  const microphonePermission = usePermission('microphone')
  const battery = useBattery()
  const motion = useDeviceMotion()
  const orientation = useDeviceOrientation()

  // 设备能力检测
  const deviceCapabilities = computed(() => ({
    hasCamera: devices.value.some(device => device.kind === 'videoinput'),
    hasMicrophone: devices.value.some(device => device.kind === 'audioinput'),
    hasSpeaker: devices.value.some(device => device.kind === 'audiooutput'),
    supportsBattery: battery.isSupported,
    supportsMotion: motion.isSupported,
    supportsOrientation: orientation.isSupported
  }))

  // 设备状态
  const deviceStatus = computed(() => ({
    batteryLevel: battery.level.value,
    isCharging: battery.charging.value,
    orientation: orientation.alpha.value ? 'landscape' : 'portrait',
    isMoving: motion.acceleration.value ?
      Math.abs(motion.acceleration.value.x || 0) > 0.1 ||
      Math.abs(motion.acceleration.value.y || 0) > 0.1 ||
      Math.abs(motion.acceleration.value.z || 0) > 0.1 : false
  }))

  return {
    devices,
    deviceCapabilities,
    deviceStatus,
    permissions: {
      camera: cameraPermission,
      microphone: microphonePermission
    },
    battery,
    motion,
    orientation
  }
}
```

#### 10.2.4 键盘快捷键

```typescript
// src/composables/vueuse/useKeyboardShortcuts.ts
import { useMagicKeys, whenever } from '@vueuse/core'
import { useRouter } from 'vue-router'
import { useCartStore } from '@/stores'

export function useKeyboardShortcuts() {
  const keys = useMagicKeys()
  const router = useRouter()
  const cartStore = useCartStore()

  // 快捷键定义
  const shortcuts = {
    // Ctrl + S: 保存
    save: keys['Ctrl+S'],
    // Ctrl + Z: 撤销
    undo: keys['Ctrl+Z'],
    // Ctrl + Y: 重做
    redo: keys['Ctrl+Y'],
    // F1: 帮助
    help: keys['F1'],
    // F2: 收银台
    cashier: keys['F2'],
    // F3: 商品管理
    products: keys['F3'],
    // F4: 订单管理
    orders: keys['F4'],
    // F5: 刷新
    refresh: keys['F5'],
    // Escape: 取消/关闭
    cancel: keys['Escape'],
    // Enter: 确认
    confirm: keys['Enter'],
    // Delete: 删除
    delete: keys['Delete']
  }

  // 注册快捷键事件
  whenever(shortcuts.save, (e) => {
    e.preventDefault()
    // 触发保存事件
    window.dispatchEvent(new CustomEvent('keyboard-save'))
  })

  whenever(shortcuts.cashier, () => {
    router.push('/cashier')
  })

  whenever(shortcuts.products, () => {
    router.push('/product')
  })

  whenever(shortcuts.orders, () => {
    router.push('/order')
  })

  whenever(shortcuts.refresh, (e) => {
    e.preventDefault()
    window.location.reload()
  })

  whenever(shortcuts.cancel, () => {
    // 触发取消事件
    window.dispatchEvent(new CustomEvent('keyboard-cancel'))
  })

  whenever(shortcuts.delete, () => {
    // 触发删除事件
    window.dispatchEvent(new CustomEvent('keyboard-delete'))
  })

  return {
    shortcuts,
    keys
  }
}
```

#### 10.2.5 拖拽功能

```typescript
// src/composables/vueuse/useDragAndDrop.ts
import { useDraggable, useDropZone } from '@vueuse/core'
import { ref, computed } from 'vue'

export function useDragAndDrop() {
  const dragElement = ref<HTMLElement>()
  const dropZone = ref<HTMLElement>()

  // 拖拽配置
  const { x, y, isDragging, style } = useDraggable(dragElement, {
    initialValue: { x: 0, y: 0 },
    preventDefault: true
  })

  // 拖放区域配置
  const { isOverDropZone } = useDropZone(dropZone, {
    onDrop(files, event) {
      // 处理文件拖放
      if (files && files.length > 0) {
        handleFileDrop(files)
      }

      // 处理元素拖放
      const dragData = event.dataTransfer?.getData('text/plain')
      if (dragData) {
        handleElementDrop(JSON.parse(dragData))
      }
    },
    onEnter() {
      console.log('进入拖放区域')
    },
    onLeave() {
      console.log('离开拖放区域')
    }
  })

  // 处理文件拖放
  const handleFileDrop = (files: File[]) => {
    files.forEach(file => {
      if (file.type.startsWith('image/')) {
        // 处理图片文件
        const reader = new FileReader()
        reader.onload = (e) => {
          const imageUrl = e.target?.result as string
          console.log('图片上传:', imageUrl)
        }
        reader.readAsDataURL(file)
      }
    })
  }

  // 处理元素拖放
  const handleElementDrop = (data: any) => {
    console.log('元素拖放:', data)
  }

  // 拖拽样式
  const dragStyle = computed(() => ({
    ...style.value,
    cursor: isDragging.value ? 'grabbing' : 'grab',
    zIndex: isDragging.value ? 1000 : 'auto'
  }))

  // 拖放区域样式
  const dropZoneStyle = computed(() => ({
    border: isOverDropZone.value ? '2px dashed #1890ff' : '2px dashed #d9d9d9',
    backgroundColor: isOverDropZone.value ? '#f0f8ff' : 'transparent'
  }))

  return {
    dragElement,
    dropZone,
    x,
    y,
    isDragging,
    isOverDropZone,
    dragStyle,
    dropZoneStyle
  }
}
```

### 10.3 与现有架构的整合

#### 10.3.1 在组件中使用 VueUse

```vue
<!-- src/components/business/ProductCard/index.vue -->
<template>
  <div
    ref="cardRef"
    :class="cardClasses"
    :style="cardStyle"
    @click="handleClick"
  >
    <!-- 商品卡片内容 -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  useElementHover,
  useElementVisibility,
  useIntersectionObserver,
  useResizeObserver
} from '@vueuse/core'

const cardRef = ref<HTMLElement>()

// 悬停状态
const isHovered = useElementHover(cardRef)

// 可见性检测
const isVisible = useElementVisibility(cardRef)

// 交叉观察器（懒加载）
const { stop } = useIntersectionObserver(
  cardRef,
  ([{ isIntersecting }]) => {
    if (isIntersecting) {
      // 元素进入视口，可以加载图片等资源
      loadCardData()
      stop() // 停止观察
    }
  },
  { threshold: 0.1 }
)

// 尺寸变化监听
useResizeObserver(cardRef, (entries) => {
  const entry = entries[0]
  console.log('卡片尺寸变化:', entry.contentRect)
})

// 动态样式
const cardStyle = computed(() => ({
  transform: isHovered.value ? 'scale(1.02)' : 'scale(1)',
  transition: 'transform 0.2s ease',
  opacity: isVisible.value ? 1 : 0.5
}))

const loadCardData = () => {
  // 懒加载逻辑
}
</script>
```

#### 10.3.2 在 Pinia Store 中使用 VueUse

```typescript
// src/stores/modules/app.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useStorage, usePreferredDark, useToggle } from '@vueuse/core'

export const useAppStore = defineStore('app', () => {
  // 使用 VueUse 的存储功能
  const settings = useStorage('app-settings', {
    sidebarCollapsed: false,
    showNotifications: true,
    autoSave: true,
    language: 'zh-CN'
  })

  // 暗色模式偏好
  const prefersDark = usePreferredDark()
  const isDark = useStorage('dark-mode', prefersDark)
  const toggleDark = useToggle(isDark)

  // 加载状态
  const isLoading = ref(false)
  const loadingText = ref('')

  // 通知列表
  const notifications = ref<Array<{
    id: string
    type: 'info' | 'success' | 'warning' | 'error'
    title: string
    message: string
    timestamp: number
  }>>([])

  // 计算属性
  const unreadNotifications = computed(() =>
    notifications.value.filter(n => !n.read)
  )

  // 操作
  const setLoading = (loading: boolean, text = '') => {
    isLoading.value = loading
    loadingText.value = text
  }

  const addNotification = (notification: Omit<typeof notifications.value[0], 'id' | 'timestamp'>) => {
    notifications.value.unshift({
      ...notification,
      id: Date.now().toString(),
      timestamp: Date.now()
    })
  }

  const removeNotification = (id: string) => {
    const index = notifications.value.findIndex(n => n.id === id)
    if (index > -1) {
      notifications.value.splice(index, 1)
    }
  }

  return {
    // 状态
    settings,
    isDark,
    isLoading,
    loadingText,
    notifications,

    // 计算属性
    unreadNotifications,

    // 操作
    toggleDark,
    setLoading,
    addNotification,
    removeNotification
  }
})
```

#### 10.3.3 全局组合式函数

```typescript
// src/composables/vueuse/useGlobalFeatures.ts
import {
  useTitle,
  useFavicon,
  useFullscreen,
  useClipboard,
  useShare,
  useWakeLock
} from '@vueuse/core'
import { computed } from 'vue'

export function useGlobalFeatures() {
  // 页面标题管理
  const title = useTitle('POS 系统')

  // 网站图标管理
  const favicon = useFavicon()

  // 全屏功能
  const { isFullscreen, enter: enterFullscreen, exit: exitFullscreen, toggle: toggleFullscreen } = useFullscreen()

  // 剪贴板功能
  const { copy, copied, isSupported: clipboardSupported } = useClipboard()

  // 分享功能
  const { share, isSupported: shareSupported } = useShare()

  // 屏幕常亮
  const { isSupported: wakeLockSupported, isActive: isWakeLockActive, request: requestWakeLock, release: releaseWakeLock } = useWakeLock()

  // 设置页面标题
  const setPageTitle = (pageTitle: string) => {
    title.value = `${pageTitle} - POS 系统`
  }

  // 复制文本到剪贴板
  const copyToClipboard = async (text: string) => {
    if (clipboardSupported.value) {
      await copy(text)
      return copied.value
    }
    return false
  }

  // 分享内容
  const shareContent = async (content: { title: string; text: string; url?: string }) => {
    if (shareSupported.value) {
      try {
        await share(content)
        return true
      } catch {
        return false
      }
    }
    return false
  }

  // 保持屏幕常亮（适用于收银场景）
  const keepScreenAwake = async () => {
    if (wakeLockSupported.value && !isWakeLockActive.value) {
      await requestWakeLock()
    }
  }

  return {
    // 状态
    title,
    favicon,
    isFullscreen,
    copied,
    isWakeLockActive,

    // 功能支持检测
    clipboardSupported,
    shareSupported,
    wakeLockSupported,

    // 操作
    setPageTitle,
    enterFullscreen,
    exitFullscreen,
    toggleFullscreen,
    copyToClipboard,
    shareContent,
    keepScreenAwake,
    releaseWakeLock
  }
}
```

---

## 11. 代码规范和开发约定

### 11.1 TypeScript 编码规范

#### 11.1.1 类型定义规范

```typescript
// ===== 接口命名 =====
// 使用 PascalCase，以 I 开头（可选）
interface UserInfo {
  id: string
  name: string
  email: string
}

// 或者不使用 I 前缀（推荐）
interface ProductData {
  id: string
  name: string
  price: number
}

// ===== 类型别名 =====
// 使用 PascalCase
type OrderStatus = 'pending' | 'confirmed' | 'shipped' | 'delivered' | 'cancelled'
type EventHandler<T> = (event: T) => void
type ApiResponse<T> = Promise<{ success: boolean; data: T; message: string }>

// ===== 枚举定义 =====
// 使用 PascalCase，成员使用 UPPER_SNAKE_CASE
enum PaymentMethod {
  CASH = 'cash',
  CARD = 'card',
  MOBILE = 'mobile',
  VOUCHER = 'voucher'
}

// 或使用 const assertion（推荐）
const PaymentMethod = {
  CASH: 'cash',
  CARD: 'card',
  MOBILE: 'mobile',
  VOUCHER: 'voucher'
} as const

type PaymentMethodType = typeof PaymentMethod[keyof typeof PaymentMethod]

// ===== 泛型约束 =====
interface Repository<T extends { id: string }> {
  findById(id: string): Promise<T | null>
  save(entity: T): Promise<T>
  delete(id: string): Promise<void>
}

// ===== 工具类型使用 =====
// 使用内置工具类型
type PartialUser = Partial<UserInfo>
type RequiredProduct = Required<ProductData>
type UserEmail = Pick<UserInfo, 'email'>
type UserWithoutId = Omit<UserInfo, 'id'>

// 自定义工具类型
type NonNullable<T> = T extends null | undefined ? never : T
type DeepReadonly<T> = {
  readonly [P in keyof T]: T[P] extends object ? DeepReadonly<T[P]> : T[P]
}
```

#### 11.1.2 函数定义规范

```typescript
// ===== 函数重载 =====
function formatPrice(price: number): string
function formatPrice(price: string): string
function formatPrice(price: number | string): string {
  const num = typeof price === 'string' ? parseFloat(price) : price
  return `¥${num.toFixed(2)}`
}

// ===== 泛型函数 =====
function createApiClient<T extends Record<string, any>>(baseURL: string): ApiClient<T> {
  return new ApiClient<T>(baseURL)
}

// ===== 高阶函数 =====
function withRetry<T extends any[], R>(
  fn: (...args: T) => Promise<R>,
  maxRetries = 3
): (...args: T) => Promise<R> {
  return async (...args: T): Promise<R> => {
    let lastError: Error

    for (let i = 0; i <= maxRetries; i++) {
      try {
        return await fn(...args)
      } catch (error) {
        lastError = error as Error
        if (i === maxRetries) break
        await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, i)))
      }
    }

    throw lastError!
  }
}

// ===== 异步函数 =====
async function fetchUserData(userId: string): Promise<UserInfo> {
  try {
    const response = await fetch(`/api/users/${userId}`)
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    return await response.json()
  } catch (error) {
    console.error('Failed to fetch user data:', error)
    throw error
  }
}
```

### 11.2 Vue 组件规范

#### 11.2.1 组件结构规范

```vue
<!-- 标准组件结构 -->
<template>
  <!-- 模板内容 -->
  <div class="component-name">
    <!-- 使用语义化的 HTML 标签 -->
    <header class="component-name__header">
      <h2 class="component-name__title">{{ title }}</h2>
    </header>

    <main class="component-name__content">
      <!-- 内容区域 -->
      <slot />
    </main>

    <footer v-if="showFooter" class="component-name__footer">
      <!-- 底部内容 -->
      <slot name="footer" />
    </footer>
  </div>
</template>

<script setup lang="ts">
// 1. 导入依赖
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import type { ComponentProps, ComponentEmits } from './types'

// 2. 定义 Props
const props = withDefaults(defineProps<ComponentProps>(), {
  title: '',
  showFooter: false,
  size: 'medium'
})

// 3. 定义 Emits
const emit = defineEmits<ComponentEmits>()

// 4. 定义响应式数据
const isLoading = ref(false)
const data = ref<any[]>([])

// 5. 定义计算属性
const computedValue = computed(() => {
  return data.value.length > 0
})

// 6. 定义方法
const handleClick = () => {
  emit('click', { timestamp: Date.now() })
}

const fetchData = async () => {
  try {
    isLoading.value = true
    // 获取数据的逻辑
  } catch (error) {
    console.error('Failed to fetch data:', error)
  } finally {
    isLoading.value = false
  }
}

// 7. 生命周期钩子
onMounted(() => {
  fetchData()
})

onUnmounted(() => {
  // 清理工作
})

// 8. 暴露给父组件的方法（可选）
defineExpose({
  fetchData,
  refresh: fetchData
})
</script>

<style lang="scss" scoped>
// 使用 BEM 命名规范
.component-name {
  // 组件根样式

  &__header {
    // 头部样式
  }

  &__title {
    // 标题样式
  }

  &__content {
    // 内容样式
  }

  &__footer {
    // 底部样式
  }

  // 修饰符
  &--large {
    // 大尺寸样式
  }

  &--small {
    // 小尺寸样式
  }
}
</style>
```

#### 11.2.2 Props 和 Emits 规范

```typescript
// src/components/ComponentName/types.ts

// Props 接口定义
export interface ComponentProps {
  // 必需属性
  id: string
  title: string

  // 可选属性
  description?: string
  disabled?: boolean
  loading?: boolean

  // 联合类型
  size?: 'small' | 'medium' | 'large'
  variant?: 'primary' | 'secondary' | 'danger'

  // 对象类型
  config?: {
    autoSave: boolean
    interval: number
  }

  // 数组类型
  items?: Array<{
    id: string
    name: string
    value: any
  }>

  // 函数类型
  validator?: (value: any) => boolean
  formatter?: (value: any) => string
}

// Emits 接口定义
export interface ComponentEmits {
  // 简单事件
  click: []
  focus: []
  blur: []

  // 带参数的事件
  change: [value: string]
  input: [value: string]
  select: [item: { id: string; name: string }]

  // 复杂事件
  'update:modelValue': [value: any]
  'item-click': [item: any, index: number]
  'before-close': [done: () => void]
}

// 默认值定义
export const defaultProps: Partial<ComponentProps> = {
  size: 'medium',
  variant: 'primary',
  disabled: false,
  loading: false
}
```

### 11.3 样式编写规范

#### 11.3.1 SCSS 编写规范

```scss
// ===== 变量命名 =====
// 使用 kebab-case，语义化命名
$primary-color: #1890ff;
$text-color-primary: #262626;
$border-radius-base: 4px;
$spacing-large: 24px;

// ===== 混入命名 =====
// 使用 kebab-case，动词开头
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin text-ellipsis($lines: 1) {
  @if $lines == 1 {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  } @else {
    display: -webkit-box;
    -webkit-line-clamp: $lines;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

// ===== BEM 命名规范 =====
.product-card {
  // 块（Block）
  padding: $spacing-base;
  border-radius: $border-radius-base;

  // 元素（Element）
  &__image {
    width: 100%;
    height: 200px;
    object-fit: cover;
  }

  &__title {
    font-size: $font-size-large;
    font-weight: 600;
    margin-bottom: $spacing-small;
    @include text-ellipsis(2);
  }

  &__price {
    color: $primary-color;
    font-size: $font-size-large;
    font-weight: 700;
  }

  &__actions {
    margin-top: $spacing-base;
    @include flex-center;
    gap: $spacing-small;
  }

  // 修饰符（Modifier）
  &--featured {
    border: 2px solid $primary-color;
    box-shadow: 0 4px 12px rgba($primary-color, 0.2);
  }

  &--disabled {
    opacity: 0.6;
    pointer-events: none;
  }

  &--large {
    .product-card__image {
      height: 300px;
    }

    .product-card__title {
      font-size: $font-size-xl;
    }
  }

  // 状态类
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
  }

  &:active {
    transform: translateY(0);
  }
}

// ===== 响应式设计 =====
.product-grid {
  display: grid;
  gap: $spacing-base;

  // 移动端
  grid-template-columns: 1fr;

  // 平板端
  @media (min-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
  }

  // 桌面端
  @media (min-width: 1024px) {
    grid-template-columns: repeat(3, 1fr);
  }

  // 大屏幕
  @media (min-width: 1440px) {
    grid-template-columns: repeat(4, 1fr);
  }
}
```

### 11.4 Git 提交规范

#### 11.4.1 提交信息格式

```bash
# 格式：<type>(<scope>): <subject>
#
# type: 提交类型
# scope: 影响范围（可选）
# subject: 简短描述

# 示例：
feat(auth): add login functionality
fix(cart): resolve quantity calculation bug
docs(readme): update installation guide
style(button): improve hover effects
refactor(api): extract common request logic
test(utils): add unit tests for format functions
chore(deps): update vue to 3.4.0
```

#### 11.4.2 提交类型说明

```bash
# feat: 新功能
feat(product): add product search functionality

# fix: 修复 bug
fix(payment): resolve payment gateway timeout issue

# docs: 文档更新
docs(api): add API documentation for user endpoints

# style: 代码格式调整（不影响功能）
style(components): fix eslint warnings

# refactor: 代码重构（不新增功能，不修复 bug）
refactor(store): simplify state management logic

# perf: 性能优化
perf(table): optimize large dataset rendering

# test: 测试相关
test(auth): add integration tests for login flow

# chore: 构建过程或辅助工具的变动
chore(build): update webpack configuration

# ci: CI/CD 相关
ci(github): add automated testing workflow

# revert: 回滚提交
revert: revert "feat(auth): add login functionality"
```

### 11.5 代码审查清单

#### 11.5.1 功能性检查

```markdown
## 功能性检查清单

### 基本功能
- [ ] 功能是否按照需求正确实现
- [ ] 边界条件是否正确处理
- [ ] 错误情况是否有适当的处理
- [ ] 用户体验是否良好

### 性能考虑
- [ ] 是否存在性能瓶颈
- [ ] 大数据量情况下是否正常工作
- [ ] 是否有不必要的重复计算
- [ ] 内存使用是否合理

### 安全性
- [ ] 用户输入是否经过验证
- [ ] 敏感信息是否正确处理
- [ ] 权限控制是否到位
- [ ] XSS/CSRF 防护是否充分
```

#### 11.5.2 代码质量检查

```markdown
## 代码质量检查清单

### 代码结构
- [ ] 代码结构是否清晰合理
- [ ] 函数/方法长度是否适中
- [ ] 是否遵循单一职责原则
- [ ] 是否有适当的抽象和封装

### 命名规范
- [ ] 变量/函数命名是否语义化
- [ ] 是否遵循项目命名规范
- [ ] 是否有拼写错误
- [ ] 缩写是否易于理解

### 注释和文档
- [ ] 复杂逻辑是否有注释说明
- [ ] 公共 API 是否有文档
- [ ] TODO/FIXME 是否有跟踪
- [ ] 注释是否与代码保持同步

### 测试覆盖
- [ ] 是否有足够的单元测试
- [ ] 测试用例是否覆盖主要场景
- [ ] 是否有集成测试
- [ ] 测试是否可以稳定运行
```

---

## 12. 构建和部署配置

### 12.1 Vite 构建配置

#### 12.1.1 基础配置

```typescript
// vite.config.ts
import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import { createHtmlPlugin } from 'vite-plugin-html'
import { visualizer } from 'rollup-plugin-visualizer'
import { VitePWA } from 'vite-plugin-pwa'

export default defineConfig(({ command, mode }) => {
  const env = loadEnv(mode, process.cwd(), '')

  return {
    plugins: [
      vue(),

      // HTML 模板处理
      createHtmlPlugin({
        inject: {
          data: {
            title: env.VITE_APP_TITLE || 'POS 系统',
            description: env.VITE_APP_DESCRIPTION || 'POS 收银系统',
          }
        }
      }),

      // 打包分析（仅在分析模式下启用）
      mode === 'analyze' && visualizer({
        filename: 'dist/stats.html',
        open: true,
        gzipSize: true,
        brotliSize: true
      }),

      // PWA 支持（可选）
      VitePWA({
        registerType: 'autoUpdate',
        workbox: {
          globPatterns: ['**/*.{js,css,html,ico,png,svg}']
        }
      })
    ].filter(Boolean),

    // 路径解析
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src'),
        '@/components': resolve(__dirname, 'src/components'),
        '@/utils': resolve(__dirname, 'src/utils'),
        '@/stores': resolve(__dirname, 'src/stores'),
        '@/api': resolve(__dirname, 'src/api'),
        '@/assets': resolve(__dirname, 'src/assets')
      }
    },

    // CSS 配置
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `
            @import "@/assets/styles/base/variables.scss";
            @import "@/assets/styles/base/mixins.scss";
          `
        }
      }
    },

    // 开发服务器配置
    server: {
      host: '0.0.0.0',
      port: 3000,
      open: false,
      cors: true,
      proxy: {
        '/api': {
          target: env.VITE_API_BASE_URL || 'http://localhost:8080',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, '')
        }
      }
    },

    // 构建配置
    build: {
      target: 'es2015',
      outDir: 'dist',
      assetsDir: 'assets',
      sourcemap: mode === 'development',
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: mode === 'production',
          drop_debugger: mode === 'production'
        }
      },

      // 代码分割
      rollupOptions: {
        output: {
          chunkFileNames: 'assets/js/[name]-[hash].js',
          entryFileNames: 'assets/js/[name]-[hash].js',
          assetFileNames: 'assets/[ext]/[name]-[hash].[ext]',

          // 手动分包
          manualChunks: {
            // Vue 相关
            vue: ['vue', 'vue-router', 'pinia'],

            // UI 组件库
            tdesign: ['tdesign-vue-next'],

            // 工具库
            utils: ['@vueuse/core', 'axios', 'date-fns'],

            // 图表库（如果使用）
            charts: ['echarts'],

            // 其他第三方库
            vendor: ['lodash-es', 'crypto-js']
          }
        }
      },

      // 资源内联阈值
      assetsInlineLimit: 4096,

      // 构建时清空输出目录
      emptyOutDir: true
    },

    // 环境变量
    define: {
      __APP_VERSION__: JSON.stringify(process.env.npm_package_version),
      __BUILD_TIME__: JSON.stringify(new Date().toISOString())
    },

    // 优化配置
    optimizeDeps: {
      include: [
        'vue',
        'vue-router',
        'pinia',
        '@vueuse/core',
        'axios',
        'tdesign-vue-next'
      ]
    }
  }
})
```

#### 12.1.2 环境配置

```bash
# .env.development
VITE_APP_TITLE=POS 系统 - 开发环境
VITE_APP_DESCRIPTION=POS 收银系统开发环境
VITE_API_BASE_URL=http://localhost:8080/api
VITE_APP_ENV=development
VITE_ENABLE_MOCK=true
VITE_ENABLE_DEVTOOLS=true

# .env.production
VITE_APP_TITLE=POS 系统
VITE_APP_DESCRIPTION=POS 收银系统
VITE_API_BASE_URL=https://api.example.com
VITE_APP_ENV=production
VITE_ENABLE_MOCK=false
VITE_ENABLE_DEVTOOLS=false

# .env.staging
VITE_APP_TITLE=POS 系统 - 测试环境
VITE_APP_DESCRIPTION=POS 收银系统测试环境
VITE_API_BASE_URL=https://staging-api.example.com
VITE_APP_ENV=staging
VITE_ENABLE_MOCK=false
VITE_ENABLE_DEVTOOLS=true
```

### 12.2 TypeScript 配置

#### 12.2.1 TypeScript 编译配置

```json
// tsconfig.json
{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,

    /* 模块解析 */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "preserve",

    /* 严格类型检查 */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,

    /* 路径映射 */
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@/components/*": ["src/components/*"],
      "@/utils/*": ["src/utils/*"],
      "@/stores/*": ["src/stores/*"],
      "@/api/*": ["src/api/*"],
      "@/assets/*": ["src/assets/*"]
    },

    /* 类型定义 */
    "types": ["vite/client", "node"],

    /* 其他选项 */
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.d.ts",
    "src/**/*.tsx",
    "src/**/*.vue"
  ],
  "exclude": [
    "node_modules",
    "dist"
  ],
  "references": [
    { "path": "./tsconfig.node.json" }
  ]
}
```

#### 12.2.2 Node.js 环境配置

```json
// tsconfig.node.json
{
  "compilerOptions": {
    "composite": true,
    "skipLibCheck": true,
    "module": "ESNext",
    "moduleResolution": "bundler",
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "types": ["node"]
  },
  "include": [
    "vite.config.ts",
    "vitest.config.ts"
  ]
}
```

### 12.3 代码质量工具配置

#### 12.3.1 ESLint 配置

```javascript
// .eslintrc.cjs
module.exports = {
  root: true,
  env: {
    browser: true,
    es2021: true,
    node: true
  },
  extends: [
    'eslint:recommended',
    '@vue/eslint-config-typescript',
    '@vue/eslint-config-prettier/skip-formatting'
  ],
  parserOptions: {
    ecmaVersion: 'latest',
    sourceType: 'module'
  },
  rules: {
    // Vue 相关规则
    'vue/multi-word-component-names': 'off',
    'vue/no-unused-vars': 'error',
    'vue/no-mutating-props': 'error',
    'vue/require-default-prop': 'error',
    'vue/require-prop-types': 'error',

    // TypeScript 相关规则
    '@typescript-eslint/no-unused-vars': 'error',
    '@typescript-eslint/no-explicit-any': 'warn',
    '@typescript-eslint/explicit-function-return-type': 'off',
    '@typescript-eslint/explicit-module-boundary-types': 'off',

    // 通用规则
    'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'prefer-const': 'error',
    'no-var': 'error',
    'object-shorthand': 'error',
    'prefer-template': 'error'
  },
  overrides: [
    {
      files: ['*.vue'],
      parser: 'vue-eslint-parser',
      parserOptions: {
        parser: '@typescript-eslint/parser'
      }
    }
  ]
}
```

#### 12.3.2 Prettier 配置

```json
// .prettierrc
{
  "semi": false,
  "singleQuote": true,
  "tabWidth": 2,
  "trailingComma": "none",
  "printWidth": 100,
  "bracketSpacing": true,
  "arrowParens": "avoid",
  "endOfLine": "lf",
  "vueIndentScriptAndStyle": false
}
```

#### 12.3.3 Stylelint 配置

```json
// .stylelintrc.json
{
  "extends": [
    "stylelint-config-standard-scss",
    "stylelint-config-recommended-vue/scss"
  ],
  "rules": {
    "selector-class-pattern": "^[a-z][a-z0-9]*(-[a-z0-9]+)*(__[a-z0-9]+(-[a-z0-9]+)*)?(--[a-z0-9]+(-[a-z0-9]+)*)?$",
    "scss/at-rule-no-unknown": [
      true,
      {
        "ignoreAtRules": ["tailwind", "apply", "variants", "responsive", "screen"]
      }
    ],
    "declaration-block-trailing-semicolon": null,
    "no-descending-specificity": null
  },
  "ignoreFiles": [
    "dist/**/*",
    "node_modules/**/*"
  ]
}
```

### 12.4 构建脚本

#### 12.4.1 Package.json 脚本

```json
{
  "scripts": {
    "dev": "vite",
    "build": "vue-tsc && vite build",
    "build:staging": "vue-tsc && vite build --mode staging",
    "build:analyze": "vue-tsc && vite build --mode analyze",
    "preview": "vite preview",
    "type-check": "vue-tsc --noEmit",
    "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix",
    "lint:style": "stylelint \"src/**/*.{css,scss,vue}\" --fix",
    "format": "prettier --write src/",
    "test": "vitest",
    "test:ui": "vitest --ui",
    "test:coverage": "vitest --coverage",
    "prepare": "husky install"
  }
}
```

#### 12.4.2 Git Hooks 配置

```bash
# .husky/pre-commit
#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

npx lint-staged
```

```json
// package.json 中的 lint-staged 配置
{
  "lint-staged": {
    "*.{vue,js,ts}": [
      "eslint --fix",
      "prettier --write"
    ],
    "*.{css,scss,vue}": [
      "stylelint --fix"
    ],
    "*.{json,md}": [
      "prettier --write"
    ]
  }
}
```

### 12.5 部署配置

#### 12.5.1 Docker 配置

```dockerfile
# Dockerfile
# 构建阶段
FROM node:18-alpine as build-stage

WORKDIR /app

# 复制 package 文件
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 生产阶段
FROM nginx:alpine as production-stage

# 复制构建产物
COPY --from=build-stage /app/dist /usr/share/nginx/html

# 复制 nginx 配置
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

#### 12.5.2 Nginx 配置

```nginx
# nginx.conf
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;

    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;

    # Gzip 压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    server {
        listen 80;
        server_name localhost;
        root /usr/share/nginx/html;
        index index.html;

        # 静态资源缓存
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # SPA 路由支持
        location / {
            try_files $uri $uri/ /index.html;
        }

        # API 代理（如果需要）
        location /api/ {
            proxy_pass http://backend:8080/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # 安全头
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header Referrer-Policy "no-referrer-when-downgrade" always;
        add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    }
}
```

---

## 13. 实施步骤规划

### 13.1 第一阶段：基础框架和核心组件库构建

#### 13.1.1 项目初始化（1-2 天）

**任务清单：**
- [ ] 创建 Vue 3 + TypeScript + Vite 项目
- [ ] 配置开发环境（ESLint、Prettier、Stylelint）
- [ ] 设置 Git 工作流和提交规范
- [ ] 配置 CI/CD 基础流程
- [ ] 建立项目目录结构

**技术要点：**
```bash
# 项目初始化命令
npm create vue@latest pos-web
cd pos-web
npm install

# 安装额外依赖
npm install tdesign-vue-next @vueuse/core axios pinia
npm install -D @types/node sass stylelint husky lint-staged
```

**交付物：**
- 完整的项目脚手架
- 开发环境配置文件
- 基础的构建和部署脚本

#### 13.1.2 基础架构搭建（3-5 天）

**任务清单：**
- [ ] 实现路由系统和路由守卫
- [ ] 搭建 Pinia 状态管理架构
- [ ] 实现 Axios 请求封装和拦截器
- [ ] 建立原生通信桥接层（JSBridge）
- [ ] 实现响应式布局系统
- [ ] 配置主题系统

**核心文件：**
```typescript
// 关键实现文件
src/router/index.ts           // 路由配置
src/stores/index.ts           // 状态管理
src/api/request.ts            // HTTP 客户端
src/bridge/index.ts           // 原生通信
src/composables/useResponsive.ts  // 响应式布局
src/composables/useTheme.ts   // 主题系统
```

**交付物：**
- 完整的基础架构代码
- 架构设计文档
- API 接口规范文档

#### 13.1.3 基础组件库开发（5-8 天）

**任务清单：**
- [ ] 开发基础 UI 组件（Button、Input、Modal 等）
- [ ] 实现布局组件（Header、Sidebar、Container 等）
- [ ] 建立组件文档和示例
- [ ] 编写组件单元测试
- [ ] 实现组件主题适配

**组件优先级：**
1. **高优先级**：Button、Input、Modal、Table、Form
2. **中优先级**：Card、Loading、Empty、Pagination
3. **低优先级**：Tooltip、Popover、Drawer

**交付物：**
- 完整的基础组件库
- 组件使用文档
- 组件测试用例

#### 13.1.4 业务组件开发（8-12 天）

**任务清单：**
- [ ] 开发商品相关组件（ProductCard、ProductSelector）
- [ ] 实现订单相关组件（OrderList、OrderItem）
- [ ] 开发支付相关组件（PaymentPanel、PaymentMethod）
- [ ] 实现收银相关组件（CashierKeyboard、ReceiptPreview）
- [ ] 开发会员相关组件（MemberInfo、MemberSelector）

**组件设计原则：**
- 高度可复用，支持多种配置
- 完整的 TypeScript 类型支持
- 良好的性能表现
- 符合无障碍访问标准

**交付物：**
- 完整的业务组件库
- 组件交互规范
- 组件性能测试报告

#### 13.1.5 工具函数和公共方法（3-5 天）

**任务清单：**
- [ ] 实现通用工具函数（格式化、验证、日期处理）
- [ ] 开发业务工具函数（价格计算、库存管理）
- [ ] 实现 UI 工具函数（动画、响应式）
- [ ] 集成 VueUse 功能模块
- [ ] 编写工具函数测试

**工具函数分类：**
```typescript
// 工具函数组织结构
src/utils/
├── common/          // 通用工具
│   ├── format.ts    // 格式化
│   ├── validate.ts  // 验证
│   └── date.ts      // 日期处理
├── business/        // 业务工具
│   ├── price.ts     // 价格计算
│   └── inventory.ts // 库存管理
└── ui/              // UI 工具
    └── animation.ts // 动画效果
```

**交付物：**
- 完整的工具函数库
- 函数使用文档
- 单元测试覆盖

### 13.2 第二阶段：业务页面开发

#### 13.2.1 认证模块（2-3 天）

**页面列表：**
- [ ] 登录页面
- [ ] 密码重置页面
- [ ] 用户信息页面

**功能要求：**
- 支持多种登录方式（用户名/手机号）
- 记住登录状态
- 自动登录功能
- 密码强度验证

#### 13.2.2 收银模块（8-12 天）

**页面列表：**
- [ ] 收银主页面
- [ ] 商品选择页面
- [ ] 购物车页面
- [ ] 支付页面
- [ ] 小票预览页面

**核心功能：**
- 商品扫码添加
- 购物车管理
- 多种支付方式
- 会员积分计算
- 小票打印

#### 13.2.3 商品管理模块（6-8 天）

**页面列表：**
- [ ] 商品列表页面
- [ ] 商品详情页面
- [ ] 商品创建/编辑页面
- [ ] 商品分类管理页面

**功能要求：**
- 商品信息管理
- 批量操作
- 图片上传
- 库存管理
- 价格管理

#### 13.2.4 订单管理模块（5-7 天）

**页面列表：**
- [ ] 订单列表页面
- [ ] 订单详情页面
- [ ] 退款处理页面
- [ ] 订单统计页面

**功能要求：**
- 订单查询和筛选
- 订单状态管理
- 退款处理
- 订单打印

#### 13.2.5 会员管理模块（4-6 天）

**页面列表：**
- [ ] 会员列表页面
- [ ] 会员详情页面
- [ ] 会员创建/编辑页面
- [ ] 会员积分管理页面

**功能要求：**
- 会员信息管理
- 积分管理
- 会员等级
- 消费记录

#### 13.2.6 库存管理模块（4-6 天）

**页面列表：**
- [ ] 库存列表页面
- [ ] 库存调整页面
- [ ] 库存调拨页面
- [ ] 库存预警页面

**功能要求：**
- 库存查询
- 库存调整
- 库存预警
- 库存报表

#### 13.2.7 报表分析模块（6-8 天）

**页面列表：**
- [ ] 销售报表页面
- [ ] 商品报表页面
- [ ] 会员报表页面
- [ ] 财务报表页面

**功能要求：**
- 多维度数据分析
- 图表展示
- 数据导出
- 自定义报表

#### 13.2.8 系统设置模块（3-5 天）

**页面列表：**
- [ ] 通用设置页面
- [ ] 门店设置页面
- [ ] 支付设置页面
- [ ] 打印设置页面

**功能要求：**
- 系统参数配置
- 门店信息管理
- 支付方式配置
- 打印模板设置

---

## 14. 工时预估与人员配置

### 14.1 第一阶段工时预估

#### 14.1.1 基础框架搭建

| 任务模块 | 预估工时 | 人员要求 | 说明 |
|---------|---------|---------|------|
| 项目初始化 | 16 小时 | 高级开发者 | 需要丰富的工程化经验 |
| 基础架构搭建 | 32 小时 | 高级开发者 | 架构设计和核心功能实现 |
| 基础组件库 | 56 小时 | 高级开发者 | 组件设计和实现 |
| 业务组件库 | 80 小时 | 高级开发者 | 复杂业务逻辑封装 |
| 工具函数库 | 32 小时 | 中级开发者 | 工具函数开发和测试 |
| **小计** | **216 小时** | **27 工作日** | **约 5.4 周** |

#### 14.1.2 人员配置建议

**第一阶段团队配置：**
- **架构师/高级前端开发者** × 1：负责整体架构设计和核心功能实现
- **高级前端开发者** × 1：负责组件库开发和复杂业务逻辑
- **中级前端开发者** × 1：负责工具函数、测试用例和文档编写

**并行开发策略：**
```mermaid
gantt
    title 第一阶段开发计划
    dateFormat  YYYY-MM-DD
    section 基础设施
    项目初始化           :done, init, 2024-01-01, 2d
    架构搭建            :done, arch, after init, 4d
    section 组件开发
    基础组件            :active, base-comp, after arch, 7d
    业务组件            :biz-comp, after base-comp, 10d
    section 工具开发
    工具函数            :utils, after arch, 4d
    测试用例            :test, after utils, 3d
```

### 14.2 第二阶段工时预估

#### 14.2.1 业务页面开发

| 模块 | 页面数量 | 预估工时 | 复杂度 | 人员要求 |
|------|---------|---------|--------|----------|
| 认证模块 | 3 | 20 小时 | 简单 | 中级开发者 |
| 收银模块 | 5 | 80 小时 | 复杂 | 高级开发者 |
| 商品管理 | 4 | 56 小时 | 中等 | 中级开发者 |
| 订单管理 | 4 | 48 小时 | 中等 | 中级开发者 |
| 会员管理 | 4 | 40 小时 | 中等 | 中级开发者 |
| 库存管理 | 4 | 40 小时 | 中等 | 中级开发者 |
| 报表分析 | 4 | 56 小时 | 复杂 | 高级开发者 |
| 系统设置 | 4 | 32 小时 | 简单 | 中级开发者 |
| **小计** | **32** | **372 小时** | - | **约 46.5 工作日** |

#### 14.2.2 页面复杂度分析

**复杂页面（需要高级开发者）：**
- 收银主页面：复杂的状态管理和实时计算
- 支付页面：多种支付方式和异步处理
- 报表页面：复杂的数据处理和图表展示

**中等复杂页面（中级开发者可胜任）：**
- 商品管理页面：标准的 CRUD 操作
- 订单管理页面：列表展示和状态管理
- 会员管理页面：信息管理和积分计算

**简单页面（初级开发者可参与）：**
- 设置页面：表单处理和配置管理
- 详情页面：数据展示为主

### 14.3 总体工时预估

#### 14.3.1 开发阶段汇总

| 阶段 | 工时 | 工作日 | 周数 | 主要内容 |
|------|------|--------|------|----------|
| 第一阶段 | 216 小时 | 27 天 | 5.4 周 | 基础框架和组件库 |
| 第二阶段 | 372 小时 | 46.5 天 | 9.3 周 | 业务页面开发 |
| 测试和优化 | 80 小时 | 10 天 | 2 周 | 集成测试和性能优化 |
| 文档和培训 | 40 小时 | 5 天 | 1 周 | 文档完善和团队培训 |
| **总计** | **708 小时** | **88.5 天** | **17.7 周** | **约 4.4 个月** |

#### 14.3.2 风险缓冲

考虑到实际开发中的不确定因素，建议增加 20% 的时间缓冲：

- **实际预估总工时**：708 × 1.2 = **850 小时**
- **实际预估工期**：88.5 × 1.2 = **106 工作日**
- **实际预估周期**：约 **5.3 个月**

### 14.4 人员配置方案

#### 14.4.1 理想团队配置

**核心开发团队（4 人）：**
- **前端架构师** × 1：负责整体架构设计和技术决策
- **高级前端开发者** × 2：负责复杂功能和核心模块开发
- **中级前端开发者** × 1：负责标准功能和测试用例开发

**支持团队（2 人）：**
- **UI/UX 设计师** × 1：负责界面设计和用户体验优化
- **测试工程师** × 1：负责功能测试和自动化测试

#### 14.4.2 最小团队配置

**核心开发团队（3 人）：**
- **高级前端开发者** × 2：分别负责基础框架和业务功能
- **中级前端开发者** × 1：负责辅助开发和测试

**预估工期调整：**
- 最小团队配置下预估工期：**6-7 个月**
- 需要更严格的项目管理和任务分配

#### 14.4.3 人员技能要求

**高级前端开发者技能要求：**
- 5+ 年前端开发经验
- 精通 Vue 3、TypeScript、现代前端工程化
- 有大型项目架构设计经验
- 熟悉性能优化和最佳实践
- 具备团队协作和指导能力

**中级前端开发者技能要求：**
- 3+ 年前端开发经验
- 熟练使用 Vue、TypeScript
- 有组件开发和测试经验
- 能够独立完成功能模块开发
- 学习能力强，能快速适应新技术

### 14.5 项目里程碑

#### 14.5.1 关键里程碑

| 里程碑 | 时间节点 | 交付物 | 验收标准 |
|--------|----------|--------|----------|
| M1: 基础框架完成 | 第 6 周 | 基础架构和组件库 | 架构稳定，组件可用 |
| M2: 核心功能完成 | 第 12 周 | 收银和商品管理模块 | 核心业务流程可用 |
| M3: 功能开发完成 | 第 16 周 | 所有业务模块 | 功能完整，基本可用 |
| M4: 测试完成 | 第 18 周 | 测试报告和优化 | 质量达标，性能良好 |
| M5: 项目交付 | 第 20 周 | 完整系统和文档 | 可正式部署使用 |

#### 14.5.2 质量控制节点

**代码审查节点：**
- 每个功能模块完成后进行代码审查
- 每周进行一次整体代码质量检查
- 重要功能上线前必须通过架构师审查

**测试验收节点：**
- 单元测试覆盖率不低于 80%
- 集成测试覆盖核心业务流程
- 性能测试满足响应时间要求
- 兼容性测试通过主流浏览器

---

## 总结

本文档详细规划了 pos-web 部分的 Vue.js 实施方案，涵盖了从项目架构设计到具体实施步骤的完整指南。通过合理的技术选型、清晰的代码组织结构、完善的开发规范和详细的工时预估，为项目的成功实施提供了坚实的基础。

**关键成功因素：**
1. **技术架构合理**：基于 Vue 3 + TypeScript 的现代化技术栈
2. **组件化设计**：高度可复用的组件库和清晰的组件分层
3. **开发规范完善**：统一的代码规范和质量控制标准
4. **工程化完备**：完整的构建、测试和部署流程
5. **团队配置合理**：根据项目复杂度配置合适的开发团队

通过严格按照本方案执行，可以确保 pos-web 项目的高质量交付，为整个 POS 系统提供稳定可靠的前端支撑。
```
```
```
```