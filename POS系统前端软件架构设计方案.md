# POS 系统前端软件架构设计方案

**目录**

- [POS 系统前端软件架构设计方案](#pos-系统前端软件架构设计方案)
  - [1. 整体架构 (Overall Architecture)](#1-整体架构-overall-architecture)
  - [2. 功能划分：原生 vs. Web](#2-功能划分原生-vs-web)
    - [2.1. 原生 (C#) 实现的功能](#21-原生-c-实现的功能)
      - [2.1.1. 外设输入的捕获与处理](#211-外设输入的捕获与处理)
      - [扫码枪](#扫码枪)
      - [延迟](#延迟)
      - [最佳实践：在原生层进行输入拦截](#最佳实践在原生层进行输入拦截)
      - [2.1.2. 进程崩溃与恢复：使用 ProcessFailed 事件](#212-进程崩溃与恢复使用-processfailed-事件)
      - [2.1.3. WebView2 Runtime 的安装与部署策略](#213-webview2-runtime-的安装与部署策略)
    - [2.2. Web (Vue 3) 实现的功能](#22-web-vue-3-实现的功能)
  - [3. 技术栈 (Tech Stack)](#3-技术栈-tech-stack)
  - [4. 数据流向设计 (Data Flow)](#4-数据流向设计-data-flow)
    - [4.1. 场景一：用户点击“打印小票”](#41-场景一用户点击打印小票)
    - [4.2. 场景二：应用启动加载商品数据](#42-场景二应用启动加载商品数据)
    - [4.3. 场景三：扫描枪扫描商品条码](#43-场景三扫描枪扫描商品条码)
  - [5. 通信规范 (Communication Protocol)](#5-通信规范-communication-protocol)
    - [5.1. 核心设计原则](#51-核心设计原则)
    - [5.2. 协议与核心数据结构](#52-协议与核心数据结构)
      - [5.2.1. 底层协议: JSON-RPC 2.0](#521-底层协议-json-rpc-20)
      - [5.2.2. 标准化业务响应: `ApiResponse<T>`](#522-标准化业务响应-apiresponset)
    - [5.3. 实现方案：类型安全的 Bridge](#53-实现方案类型安全的-bridge)
      - [5.3.1. 步骤一: 定义 API 契约 (`src/bridge/types.ts`)](#531-步骤一-定义-api-契约-srcbridgetypests)
      - [5.3.2. 步骤二: 封装底层通信服务 (`src/services/bridge.ts`)](#532-步骤二-封装底层通信服务-srcservicesbridgets)
      - [5.3.3. 步骤三: 创建强类型 API 接口 (`src/services/nativeApi.ts`)](#533-步骤三-创建强类型-api-接口-srcservicesnativeapits)
    - [5.4. 使用方法 (Usage Example)](#54-使用方法-usage-example)
    - [5.5. 安全考量 (Security Considerations)](#55-安全考量-security-considerations)
  - [6. 响应式布局方案](#6-响应式布局方案)
    - [6.1. 多分辨率适配策略](#61-多分辨率适配策略)
      - [6.1.1. CSS 变量动态缩放](#611-css-变量动态缩放)
      - [6.1.2. Vue 组合式函数：屏幕适配](#612-vue-组合式函数屏幕适配)
  - [7. 自动更新设计 (Auto-Update Design)](#7-自动更新设计-auto-update-design)
    - [7.1. 核心目标](#71-核心目标)
    - [7.2. 更新架构](#72-更新架构)
    - [7.3. 更新策略配置](#73-更新策略配置)
      - [7.3.1. 配置文件结构](#731-配置文件结构)
      - [7.3.2. 规则详解](#732-规则详解)
    - [7.4. 更新流程](#74-更新流程)
    - [7.5. 安全与回滚](#75-安全与回滚)
  - [8. 日志系统 (Logging System)](#8-日志系统-logging-system)
  - [9. 用户行为分析 (Baidu Tongji)](#9-用户行为分析-baidu-tongji)
  - [10. 项目结构设计 (Project Structure)](#10-项目结构设计-project-structure)
    - [10.1. C# 原生宿主 (`pos-native`)](#101-c-原生宿主-pos-native)
    - [10.2. Vue 前端核心 (`pos-web`)](#102-vue-前端核心-pos-web)
  - [11. 开发规范与指南 (Development Guide)](#11-开发规范与指南-development-guide)
    - [11.1. 命名规范](#111-命名规范)
    - [11.2. Git 工作流与提交规范](#112-git-工作流与提交规范)
    - [11.3. 编码规范](#113-编码规范)
    - [11.4. 代码审查 (Code Review)](#114-代码审查-code-review)
    - [11.5. 拥抱未来](#115-拥抱未来)

## 1. 整体架构 (Overall Architecture)

本方案采用业界成熟的 **原生容器与 Web 核心相结合** 的混合式架构（Hybrid Architecture），旨在融合原生技术与 Web 技术的各自优势，以实现开发效率、用户体验与系统性能的最佳平衡。

- **核心思想**: 采用轻量级原生应用（C#/.NET）作为宿主容器，承载核心的 Web 应用（Vue 3）。此模式下，原生层专注于处理底层系统交互与硬件集成等平台相关任务，而 Web 层则负责所有业务逻辑实现与用户界面（UI）的构建，从而实现开发敏捷性。

- **架构目标**:

  - **高效开发**: 充分利用 Web 前端生态系统，实现 UI 和业务逻辑的快速开发与迭代。
  - **卓越体验**: 借助原生能力，提供流畅的硬件交互和无缝的系统集成体验。
  - **平台兼容**: Web 核心确保 UI 与业务逻辑具备高度可移植性，能够平滑迁移至未来的 Android 或其他平台。
  - **稳定可靠**: 原生容器为应用提供稳健的运行环境，并实现对系统资源的有效管理。

- **分层职责**:

  - **原生容器 (Native Container - C#/.NET):** 作为应用的**宿主环境 (Hosting Environment)**，其核心职责是为 Web 核心提供一个稳定、高效且功能完备的运行时。

    - **应用生命周期管理 (Application Lifecycle):** 负责应用的初始化、主窗口（WebView2）创建与管理，以及应用的正常退出。
    - **硬件抽象层 (Hardware Abstraction Layer - HAL):** 集成并管理打印机、扫描枪等外围设备的 SDK 或驱动，提供统一的硬件访问接口，屏蔽底层硬件的复杂性与实现差异。
    - **系统能力抽象 (System-Level Abstraction):** 封装底层操作系统 API，以标准化的接口形式向 Web 层暴露系统级能力，如文件 I/O、网络状态感知及自动更新服务。
    - **安全通信桥 (Secure Communication Bridge):** 实现一个健壮的`JSBridge`，确保原生与 Web 之间进行安全、高效、异步的双向数据通信。

  - **Web 核心 (Web Core - Vue.js 3):** 作为应用的**用户界面与业务逻辑层 (UI & Business Logic Layer)**，全面负责应用的呈现与交互逻辑。
    - **声明式 UI 渲染 (Declarative UI Rendering):** 利用 Vue 的响应式系统，高效地将应用状态（State）映射并渲染为用户界面。
    - **业务逻辑编排 (Business Logic Orchestration):** 实现所有业务流程，如商品管理、订单处理、会员服务等，并对原生能力及后端服务进行调用与整合。
    - **集中式状态管理 (Centralized State Management):** 通过 Pinia 对整个应用的共享状态进行统一管理，确保数据流的可预测性与一致性。
    - **API 服务代理 (API Service Proxy):** 封装对后端服务的 HTTP 请求，作为前端与云端服务之间的数据交互代理。

**架构图:**

- 逻辑上 ，Web 层和原生层是两个协作的 并列 模块。
- 物理上 ，原生层是 宿主 (Host) ，Web 层是运行于其中的 内容 (Content) 。

```mermaid
graph TD
    subgraph "POS终端(Windows/Android)"
        subgraph "原生层 (Native Layer - C#/.NET)"
            A[应用生命周期] --> B[主窗口];
            B -- 宿主 --> C{WebView2};
            F[硬件抽象层] -- SDK/驱动 --> G[硬件外设];
            E[系统能力抽象] -- OS API --> D[操作系统];
            H[JSBridge] -- 安全通信 --> C;
            F -- 暴露接口 --> H;
            E -- 暴露接口 --> H;
        end

        subgraph "Web层 (Web Layer - Vue 3)"
            subgraph "UI/视图层 (View Layer)"
              I[页面/组件] -- 渲染 --> C;
            end
            subgraph "逻辑/状态层 (Logic & State Layer)"
              J[Vue Router];
              K[Pinia Store];
              L[API服务];
            end
            I -- 路由 --> J;
            I -- "读/写" --> K;
            I -- 调用 --> L;
            L -- 更新 --> K;
            I -- "通过Bridge调用 jsonrpc2.0" --> H;
        end
    end

    L -- "HTTP/REST" --> M[后端中台服务];

    style C fill:#f9f,stroke:#333,stroke-width:2px
    style H fill:#ccf,stroke:#333,stroke-width:2px
```

## 2. 功能划分：原生 vs. Web

### 2.1. 原生 (C#) 实现的功能

| 功能模块          | 技术实现                                           | 理由                                                                            |
| :---------------- | :------------------------------------------------- | :------------------------------------------------------------------------------ |
| 应用窗口/生命周期 | C# WPF, Microsoft.Web.WebView2 控件                | 提供无边框窗口、最小化/最大化控制、应用启动退出管理。                           |
| 外设驱动与交互    | System.IO.Ports, P/Invoke, 设备厂商 SDK            | 实现高性能、高稳定性的硬件通信，如打印机、扫描枪、钱箱。                        |
| 文件系统访问      | System.IO 命名空间                                 | 安全地读写本地日志、用户配置、缓存等。                                          |
| 自动更新服务      | 自研更新器或 Squirrel.Windows 等框架               | 实现后台下载、版本比对、灰度发布和静默/提示安装。                               |
| 网络状态监控      | .NET 网络状态 API                                  | 实时感知网络变化，通知 Web 层以实现离线友好功能。                               |
| 通信桥 (JSBridge) | WebView2 WebMessageReceived / PostWebMessageAsJson | 搭建原生与 Web 通信的可靠桥梁。使用 JSON-RPC 2.0 协议通信, 确保一致性, 扩展性。 |
| 应用配置管理      | C# 读取外部 `appsettings.json` 文件                | 实现配置（如 API 地址、门店 ID）与代码分离，便于部署。                          |
| 进程健康监控      | 订阅 `CoreWebView2.ProcessFailed` 事件             | 实时、高效地捕获 WebView2 渲染进程崩溃事件，实现应用自动恢复或提示，提升健壮性。 |
| WebView2 Runtime 部署 | 检测并引导安装 WebView2 Runtime                    | 确保应用在没有预装 WebView2 的低版本 Windows 系统上也能正常启动和运行。         |

#### 2.1.1. 外设输入的捕获与处理

#### 扫码枪

在 POS 系统中，扫码枪输入本质上是模拟高速的键盘敲击。

#### 延迟

1.  **原生 C#监听路径**

    - **流程**: `硬件` → `操作系统内核` → `Win32消息队列` → `C#全局钩子`
    - **特点**: 此路径在操作系统底层进行捕获，几乎没有延迟（约 1-5 毫秒），可以非常精确地计算按键之间的时间间隔，从而判断出高速的扫码枪输入。

2.  **WebView 前端监听路径**
    - **流程**: `硬件` → `操作系统内核` → `Win32消息队列 ` → `WebView宿主` → `Chromium渲染进程` → `JS事件循环` → `JS keydown监听器`
    - **特点**: 事件传递路径漫长，涉及多次进程间通信（IPC）。如果 Web 应用本身正忙于渲染或计算，事件处理的延迟可能高达数十甚至上百毫秒。这种不确定的延迟使得在前端通过计时器来区分快慢输入的方法变得极不可靠。

#### 最佳实践：在原生层进行输入拦截

基于以上分析，最稳定、最高效的解决方案是在原生（C#）层面进行全局输入拦截。

- **实现方式**:
  1.  通过 C#的全局键盘钩子，监听所有的键盘事件。
  2.  根据极短的按键时间间隔，精确识别出扫码枪的输入流。
  3.  当识别到一个快速的事件序列（判定为扫码枪输入）时，将组合好的完整条码信息，通过 `JSBridge` 一次性、原子性地发送给前端 Vue 应用进行后续的业务处理。
- **核心原则**：
  1. C#：只负责监听和判断，并发送完整的条码结果。
  2. JS：相信 C#发来的结果是唯一信源，并用它来驱动业务，然后清理现场。
- **优势**:
  - **可靠性高**: 从根源上隔离了两种输入类型，避免了前端复杂的、不可靠的状态判断。
  - **用户体验好**: 对用户完全透明，无论当前焦点在哪里，扫码操作都能被正确捕获和处理。
  - **性能优越**: 避免了在 WebView 中引入额外的、高频的监听器和复杂的逻辑，保证了前端应用的流畅性。

#### 2.1.2. 进程崩溃与恢复：使用 ProcessFailed 事件

为了确保 POS 系统的最高稳定性，必须能够处理 Web 内容进程（即 WebView2 的渲染进程）可能发生的崩溃或无响应。相比于传统的“心跳包”检测机制，WebView2 自身提供了一个更优的解决方案。

- **核心机制**: `CoreWebView2` 对象暴露了 `ProcessFailed` 事件。当 WebView2 的渲染进程或任何关键子进程因任何原因（如内存溢出、渲染引擎错误）意外终止时，原生应用会立即收到此事件通知。

- **优势**:
  - **及时性**: 事件触发是即时的，远快于需要等待超时才能判断的心跳机制。
  - **准确性**: 由浏览器内核直接报告进程状态，避免了网络波动等因素对心跳包造成的误判。
  - **高效性**: 无需在 Web 和原生之间建立额外的、消耗资源的定时通信，降低了系统开销。

- **实现策略**:
  1.  在 C# 原生应用初始化 WebView2 控件后，立即订阅 `ProcessFailed` 事件。
  2.  在事件处理程序中，可以根据业务需求执行恢复逻辑，例如：
      -   记录详细的崩溃日志。
      -   向用户显示一个友好的错误提示，并提供“重新加载”或“重启应用”的选项。
      -   尝试通过调用 `CoreWebView2.Reload()` 方法来恢复页面。
      -   如果多次重载失败，则引导用户重启整个应用程序。
  
通过这种方式，我们可以构建一个更加健壮的混合应用，能够从容应对前端内容的意外崩溃，最大程度地保障业务的连续性。

#### 2.1.3. WebView2 Runtime 的安装与部署策略

为了确保应用在用户的设备上（尤其是低版本的 Windows 系统）能够顺利运行，必须妥善处理 WebView2 Runtime 的依赖问题。应用启动时，应首先检测系统是否已安装合适的 WebView2 Runtime。

- **检测机制**: 
  - 在 C# 应用启动的早期阶段，调用 `Microsoft.Web.WebView2.Core.CoreWebView2Environment.GetAvailableBrowserVersionString()` 方法。
  - 如果该方法抛出 `WebView2RuntimeNotFoundException` 异常，则表明系统缺少 WebView2 Runtime。

- **部署策略**:
  根据应用的分发方式和用户群体，可以选择以下几种策略之一：
  1.  **引导用户在线安装 (Evergreen Bootstrapper)**:
      - **方式**: 在应用安装包中捆绑一个非常小的引导程序（Bootstrapper）。当检测到 Runtime 缺失时，启动该程序，它会自动从微软服务器下载并安装与用户系统架构匹配的最新版 WebView2 Runtime。
      - **优点**: 安装包体积小，用户始终能获得最新的、包含安全更新的 Runtime。
      - **缺点**: 需要用户设备在安装时能够访问互联网。
      - **适用场景**: 大多数通过网络分发的标准应用。

  2.  **捆绑离线安装包 (Evergreen Standalone Installer)**:
      - **方式**: 在应用安装包中完整地包含一个特定版本的独立安装程序。当检测到 Runtime 缺失时，静默或提示用户运行此安装程序。
      - **优点**: 无需联网即可完成安装，适合内网或离线环境。
      - **缺点**: 增大了主安装包的体积（约 100MB+）。
      - **适用场景**: 对离线环境有强需求的企业或政府客户。

  3.  **固定版本分发 (Fixed Version)**:
      - **方式**: 将特定版本的 WebView2 Runtime 的所有文件直接打包到应用目录中。应用只使用这个特定版本的 Runtime。
      - **优点**: 绿色部署，不依赖系统环境，确保了在所有设备上行为完全一致，便于测试和问题复现。
      - **缺点**: 极大地增加了应用包的体积（约 250-300MB），且需要应用自行负责 Runtime 的安全更新。
      - **适用场景**: 对环境一致性有极高要求的特殊场景，如金融、医疗等行业的专用设备。

- **用户体验优化**:
  - 当检测到需要安装 Runtime 时，应向用户显示清晰、友好的提示界面，说明“正在为您准备必要的运行环境，请稍候...”，而不是直接弹出一个技术性的错误对话框。
  - 如果可能，显示安装进度，以缓解用户的等待焦虑。
  - 安装完成后，自动重新启动应用或 WebView2 控件，无缝衔接进入主程序。

**本项目推荐策略**：优先采用 **引导用户在线安装 (Evergreen Bootstrapper)** 的方式，因为它在大多数情况下提供了最佳的平衡点：安装包小、用户能享受自动更新带来的安全性和新功能。同时，可以提供一个包含离线安装包的备用下载链接，以满足特殊网络环境下的用户需求。

### 2.2. Web (Vue 3) 实现的功能

| 功能模块      | 技术实现                              | 理由                                                                                                                                             |
| :------------ | :------------------------------------ | :----------------------------------------------------------------------------------------------------------------------------------------------- |
| 所有 UI 界面  | Vue 3, TDesign, TypeScript            | 开发进销存、会员、收银、报表等所有业务界面。                                                                                                     |
| 业务逻辑      | TypeScript                            | 处理商品计算、会员积分、促销规则等核心业务。                                                                                                     |
| 应用路由      | vue-router                            | 管理单页面应用（SPA）的视图切换。                                                                                                                |
| 状态管理      | Pinia + `pinia-plugin-persistedstate` | 全局管理购物车、登录用户、从原生接收的系统配置等共享状态，并利用插件将关键状态（如购物车）持久化至`localStorage`，防止应用意外关闭导致数据丢失。 |
| 后端 API 通信 | axios 封装                            | 与 Java 中台进行数据交互，获取和提交业务数据。                                                                                                   |
| 响应式布局    | CSS 变量动态缩放                      | 实现跨分辨率的等比缩放界面。                                                                                                                     |
| 用户行为分析  | 百度统计 + vue-router 守卫            | 收集用户行为数据以支持产品优化。                                                                                                                 |
| 日志上报      | 调用原生日志接口                      | 将前端的操作和错误日志传递给原生层进行统一记录。                                                                                                 |

## 3. 技术栈 (Tech Stack)

| 层次      | 技术/库                                                               | 说明                                                    |
| :-------- | :-------------------------------------------------------------------- | :------------------------------------------------------ |
| 原生 (C#) | .NET 6/8, WinForms/WPF, WebView2, System.Text.Json                    | 现代化的.NET 平台，高性能的 WebView2（基于 Chromium）。 |
| Web 前端  | Vue 3 (Composition API), Vite, TypeScript, TDesign, Pinia, vue-router | 高效的开发体验，强大的类型系统，高质量的 B 端组件库。   |
| 核心通信  | JSON-RPC 2.0                                                          | 轻量、无状态、规范清晰的远程过程调用协议。              |
| 构建/打包 | `dotnet publish` (C#), `npm run build` (Vue)                          | 分别打包原生应用和 Web 静态资源。                       |
| 代码规范  | ESLint, Prettier, .NET Code Style                                     | 统一团队代码风格，提升代码质量和可维护性。              |

## 4. 数据流向设计 (Data Flow)

### 4.1. 场景一：用户点击“打印小票”

```mermaid
sequenceDiagram
    participant Web as Web (Vue)
    participant Bridge as JSBridge
    participant Native as 原生 (C#)
    participant Printer as 打印机

    Web->>Bridge: 1. 调用打印接口 (jsonrpc request, id: uuid)
    Bridge->>Native: 2. 转发请求
    Native->>Printer: 3. 调用打印机SDK/驱动
    Printer-->>Native: 4. 返回打印状态 (成功/失败)
    Native-->>Bridge: 5. 响应Web (jsonrpc response, id: uuid)
    Bridge-->>Web: 6. 接收并处理打印结果 (Promise resolve/reject)
```

### 4.2. 场景二：应用启动加载商品数据

```mermaid
sequenceDiagram
    participant Native as 原生 (C#)
    participant Web as Web (Vue)
    participant Pinia as 状态管理
    participant API as 后端Java中台

    Native->>Web: 1. 启动并加载WebView
    Web->>API: 2. 通过Axios请求商品列表
    API-->>Web: 3. 返回商品数据
    Web->>Pinia: 4. 将数据存入Store
    Pinia-->>Web: 5. 更新UI组件，渲染商品列表
```

### 4.3. 场景三：扫描枪扫描商品条码

```mermaid
sequenceDiagram
    participant Scanner as 扫描枪
    participant Native as 原生 (C#)
    participant Bridge as JSBridge
    participant Web as Web (Vue)

    Scanner->>Native: 1. 模拟键盘输入条码+回车
    Native->>Bridge: 2. 捕获输入，判断为扫码事件
    Bridge->>Web: 3. 主动推送扫码事件 (jsonrpc notification)
    Web->>Web: 4. 接收事件，在收银台页面添加对应商品
```

## 5. 通信规范 (Communication Protocol)

Web 端与原生应用（Windows C# / Android）的通信是混合应用的核心。为确保通信的 **健壮性、可维护性、类型安全和易用性**，我们设计了以下分层通信规范。

### 5.1. 核心设计原则

1.  **类型安全 (Type-Safe)**: 利用 TypeScript 在开发阶段捕获所有接口调用的类型错误，杜绝运行时因参数或返回值类型不匹配导致的问题。
2.  **契约驱动 (Contract-Driven)**: 所有 Web-Native 交互都必须在统一的“API 契约”中明确定义。此契约是双方通信的唯一真实来源 (Single Source of Truth)。
3.  **关注点分离 (Separation of Concerns)**: 将底层的 JSON-RPC 消息处理逻辑与上层的业务 API 调用完全分离。业务开发者只需关心调用哪个方法，而无需处理复杂的消息收发、ID 匹配和超时逻辑。
4.  **跨平台兼容 (Cross-Platform)**: 设计一套统一的 JavaScript 接口，使其在 Windows (WebView2) 和 Android (WebView) 平台上表现一致。

### 5.2. 协议与核心数据结构

#### 5.2.1. 底层协议: JSON-RPC 2.0

我们选用轻量、成熟的 **JSON-RPC 2.0** 作为底层通信协议。它清晰地定义了三种通信模式：

-   **请求 (Request)**: Web 端调用原生方法并期望获得响应。包含 `id`。
-   **响应 (Response)**: 原生端对请求的回应，包含成功 (`result`) 或失败 (`error`) 的结果，并携带与请求相同的 `id`。
-   **通知 (Notification)**: 单向通信，一方通知另一方某事发生，不期望响应。不包含 `id`。

#### 5.2.2. 标准化业务响应: `ApiResponse<T>`

为了让业务逻辑处理更规范，我们规定所有“请求-响应”模式的调用（`invoke`）都必须返回一个标准化的业务层响应对象，而不是直接返回原生数据。

**实现思路**: 这个设计将“通信层成功”与“业务层成功”解耦。例如，一次打印请求，RPC 通信本身可能成功（网络通畅，方法找到），但打印机可能缺纸（业务失败）。`ApiResponse` 结构可以清晰地表达这种区别。

```typescript
// file: src/bridge/types.ts

/**
 * 通用的成功响应结构。
 * @template T - 成功时返回的业务数据类型。
 */
export interface SuccessResponse<T> {
  /** 操作是否成功，固定为 true */
  success: true;
  /** 业务数据负载 */
  data: T;
}

/**
 * 通用的失败响应结构。
 */
export interface ErrorResponse {
  /** 操作是否成功，固定为 false */
  success: false;
  /**
   * 业务错误码，用于程序化识别错误类型。
   * 规范建议:
   * - 1000-1999: 通用错误 (如参数无效)
   * - 2000-2999: 打印机相关错误
   * - 3000-3999: 数据库相关错误
   */
  code: number;
  /** 人类可读的错误信息，用于日志记录或向用户展示。 */
  message: string;
}

/**
 * 标准化的API响应类型。
 * 所有 invoke 方法的 Promise 都应解析为此类型。
 * @template T - 成功时返回的业务数据类型。
 */
export type ApiResponse<T> = SuccessResponse<T> | ErrorResponse;
```

### 5.3. 实现方案：类型安全的 Bridge

我们将通过三层抽象来实现一个优雅、类型安全的通信方案：

1.  **类型定义层**: 定义所有通信契约和数据结构。
2.  **通信服务层**: 封装底层的消息收发、请求匹配、超时和错误处理。
3.  **API 接口层**: 提供给业务代码使用的、完全类型安全的 `nativeApi` 对象。

#### 5.3.1. 步骤一: 定义 API 契约 (`src/bridge/types.ts`)

这是整个方案的基石。我们在此文件中定义所有与原生交互的接口、参数和返回值的类型。

**实现思路**: 通过一个 `ApiContract` 接口，我们将所有通信点集中管理。这使得接口变更、查找和维护变得极其简单。TypeScript 将利用这个接口为我们提供强大的自动补全和类型检查。

```typescript
// file: src/bridge/types.ts

// --- 1. 底层 JSON-RPC 结构 ---
export interface JsonRpcRequest<T = any> { jsonrpc: "2.0"; method: string; params?: T; id: string | number; }
export interface JsonRpcNotification<T = any> { jsonrpc: "2.0"; method: string; params?: T; }
export interface JsonRpcSuccessResponse<T = any> { jsonrpc: "2.0"; result: T; id: string | number; }
export interface JsonRpcErrorResponse { jsonrpc: "2.0"; error: { code: number; message: string; data?: any; }; id: string | number | null; }
export type JsonRpcResponse<T = any> = JsonRpcSuccessResponse<T> | JsonRpcErrorResponse;

// --- 2. 标准化业务响应 (已在上方定义) ---
// export type { ApiResponse, SuccessResponse, ErrorResponse } from './types'; // This line is conceptually correct but would be in another file.

// --- 3. 具体业务的参数与事件负载类型 ---
// [调用] 打印小票
export interface PrintParams {
  content: object; // 票据内容的结构化数据
  copies?: number; // 打印份数
}

// [通知] 记录日志
export interface LogParams {
  level: "info" | "warn" | "error";
  message: string;
  context?: object; // 附加的上下文信息
}

// [事件] 网络状态变更
export interface NetworkStatusEvent {
  online: boolean;
}

// [事件] 扫码枪数据
export interface ScannerDataEvent {
  code: string;
}

// --- 4. API 契约 (Single Source of Truth) ---
/**
 * 定义所有 Web 与 Native 之间的通信接口。
 * 这是实现端到端类型安全的核心。
 */
export interface ApiContract {
  /**
   * 请求/响应模式 (Web 调用 Native，并等待返回结果)
   * `key`: 方法名 (建议使用 `domain.action` 格式)
   * `value`: { params: 参数类型, result: 返回值类型 (必须是 ApiResponse<T>) }
   */
  requests: {
    'printer.print': {
      params: PrintParams;
      result: ApiResponse<{ jobId: string }>;
    };
    'database.query': {
      params: { sql: string; args: any[] };
      result: ApiResponse<{ rows: any[] }>;
    };
  };

  /**
   * 通知模式 (Web 调用 Native，不关心返回结果)
   * `key`: 方法名
   * `value`: { params: 参数类型 }
   */
  notifications: {
    'logger.log': { params: LogParams };
    'ui.showToast': { params: { message: string; duration: 'short' | 'long' } };
  };

  /**
   * 事件模式 (Native 主动推送到 Web)
   * `key`: 事件名
   * `value`: 事件负载的数据类型
   */
  events: {
    'network.statusChanged': NetworkStatusEvent;
    'scanner.dataReceived': ScannerDataEvent;
  };
}
```

#### 5.3.2. 步骤二: 封装底层通信服务 (`src/services/bridge.ts`)

该服务负责处理所有“脏活累活”，为上层提供干净的 `invoke`, `notify`, `subscribe` 方法。

**实现思路**: `BridgeService` 是一个单例，它在初始化时检测当前环境（Windows 或 Android），并设置相应的消息监听器。它内部维护一个 `pendingRequests` 映射表，用于在收到响应时，能准确地将结果传递给对应的 Promise。同时，它还管理着事件的订阅与发布。

```typescript
// file: src/services/bridge.ts
import { v4 as uuidv4 } from "uuid";
import type { JsonRpcRequest, JsonRpcResponse, JsonRpcNotification } from "@/bridge/types";

// --- 类型定义 ---
type PendingRequest = { resolve: (value: any) => void; reject: (reason?: any) => void; timeoutTimer: number; };
export type EventListener = (params: any) => void;

// --- 全局 Window 接口扩展 ---
declare global {
  interface Window {
    chrome?: { webview?: { postMessage: (msg: any) => void; addEventListener: (type: string, handler: (event: { data: any }) => void) => void; }; };
    AndroidBridge?: { postMessage: (msg: string) => void; };
    onNativeMessage?: (msg: string) => void;
  }
}

/**
 * 底层通信服务，处理与原生环境的 JSON-RPC 消息交换。
 * 这个类被设计为内部使用，业务代码不应直接与之交互。
 */
class BridgeService {
  private pendingRequests = new Map<string | number, PendingRequest>();
  private eventListeners = new Map<string, Set<EventListener>>();
  private readonly DEFAULT_TIMEOUT = 15000; // 15秒超时
  private platform: "windows" | "android" | "unknown" = "unknown";

  constructor() {
    this.detectPlatform();
    this.setupMessageListener();
  }

  // 1. 环境检测与监听设置
  private detectPlatform(): void {
    if (window.chrome?.webview) this.platform = "windows";
    else if (window.AndroidBridge) this.platform = "android";
    else {
        this.platform = "unknown";
        this._logError("Bridge platform detection failed. No native bridge found.");
    }
  }

  private setupMessageListener(): void {
    if (this.platform === 'windows') {
      window.chrome!.webview!.addEventListener("message", (event) => this.handleNativeMessage(event.data));
    } else if (this.platform === 'android') {
      window.onNativeMessage = (message: string) => {
        try {
          this.handleNativeMessage(JSON.parse(message));
        } catch (e) {
          this._logError("Failed to parse message from Android", { originalMessage: message, error: e });
        }
      };
    }
  }

  // 2. 核心消息处理
  private handleNativeMessage(message: JsonRpcResponse | JsonRpcNotification): void {
    // Case A: 是一个响应 (有 id)
    if ("id" in message && message.id !== null) {
      const pending = this.pendingRequests.get(message.id);
      if (pending) {
        clearTimeout(pending.timeoutTimer);
        if ("result" in message) {
          pending.resolve(message.result);
        } else {
          this._logError("Received error response from native", { id: message.id, error: message.error });
          pending.reject(message.error);
        }
        this.pendingRequests.delete(message.id);
      }
    } 
    // Case B: 是一个事件/通知 (没有 id)
    else {
      this.publish(message.method, message.params);
    }
  }

  // 3. 核心消息发送
  private postMessage(message: JsonRpcRequest | JsonRpcNotification): void {
    try {
      if (this.platform === 'windows') {
        window.chrome!.webview!.postMessage(message);
      } else if (this.platform === 'android') {
        window.AndroidBridge!.postMessage(JSON.stringify(message));
      } else {
        // 这个错误理论上在 detectPlatform 时已经记录，但作为防御性编程保留
        throw new Error("Native bridge is not available.");
      }
    } catch (error) {
        this._logError(`Failed to post message for method: ${message.method}`, { message, error });
        // 重新抛出错误，让调用者知道发送失败
        throw error;
    }
  }

  // 4. 暴露给上层 API 的公共方法
  public invoke<TResult = any, TParams = any>(method: string, params: TParams): Promise<TResult> {
    const id = uuidv4();
    const request: JsonRpcRequest<TParams> = { jsonrpc: "2.0", method, params, id };

    return new Promise<TResult>((resolve, reject) => {
      const timeoutTimer = window.setTimeout(() => {
        this.pendingRequests.delete(id);
        const error = { code: -32000, message: `Request timed out after ${this.DEFAULT_TIMEOUT}ms` };
        this._logError(`Request timed out for method: ${method}`, { id, timeout: this.DEFAULT_TIMEOUT });
        reject(error);
      }, this.DEFAULT_TIMEOUT);

      this.pendingRequests.set(id, { resolve, reject, timeoutTimer });

      try { this.postMessage(request); }
      catch (e) {
        clearTimeout(timeoutTimer);
        this.pendingRequests.delete(id);
        reject({ code: -32001, message: "Failed to send message to native", data: e });
      }
    });
  }

  public notify<TParams = any>(method: string, params: TParams): void {
    const notification: JsonRpcNotification<TParams> = { jsonrpc: "2.0", method, params };
    try {
      this.postMessage(notification);
    } catch (e) {
      // 虽然 notify 是“即发即忘”，但记录发送失败的错误对于调试仍然至关重要
      this._logError(`Failed to send notification for method: ${method}`, { error: e });
    }
  }

  public subscribe(eventName: string, listener: EventListener): () => void {
    if (!this.eventListeners.has(eventName)) {
      this.eventListeners.set(eventName, new Set());
    }
    this.eventListeners.get(eventName)!.add(listener);
    return () => { this.eventListeners.get(eventName)?.delete(listener); };
  }

  private publish(eventName: string, data: any): void {
    this.eventListeners.get(eventName)?.forEach((listener) => {
      try {
        listener(data);
      } catch (e) {
        this._logError(`Error in event listener for '${eventName}'`, { error: e });
      }
    });
  }

  // 5. 统一错误处理
  private _logError(summary: string, context: object = {}): void {
    console.error(`[BridgeService] ${summary}`, context);
    // 在未来，这里可以扩展为上报到远程日志服务，如 Sentry, LogRocket 等
    // e.g., Sentry.captureException(new Error(summary), { extra: context });
  }
}

// 导出 BridgeService 的单例，供 nativeApi 使用
export const bridge = new BridgeService();
```

#### 5.3.3. 步骤三: 创建强类型 API 接口 (`src/services/nativeApi.ts`)

为了在业务代码中提供极致的类型安全和开发体验，我们创建一个 `nativeApi` 对象。该对象为 `ApiContract` 中的每一个接口都创建一个具名的、显式的方法，使得调用就像操作一个普通的 JavaScript 对象一样简单直观。

这种显式定义的方法虽然在新增接口时需要少量重复工作，但其带来的代码清晰度和对所有水平开发者（尤其是对 TypeScript 高级特性不熟悉的开发者）的友好性，使其成为一个稳健且易于维护的选择。

**实现 (`src/services/nativeApi.ts`)**:

```typescript
// file: src/services/nativeApi.ts
import { bridge } from './bridge';
import type { 
  PrintParams, 
  LogParams, 
  NetworkStatusEvent, 
  ScannerDataEvent, 
  ApiResponse 
} from '@/bridge/types';

/**
 * 完全类型安全的 Native API 对象 (显式版本)。
 * 结构与 ApiContract 保持一致，易于理解。
 */
export const nativeApi = {
  // --- Requests (对应 ApiContract['requests']) ---
  printer: {
    print: (params: PrintParams): Promise<ApiResponse<{ jobId: string }>> => {
      return bridge.invoke('printer.print', params);
    },
  },
  database: {
    query: (params: { sql: string; args: any[] }): Promise<ApiResponse<{ rows: any[] }>> => {
      return bridge.invoke('database.query', params);
    },
  },

  // --- Notifications (对应 ApiContract['notifications']) ---
  logger: {
    log: (params: LogParams): void => {
      bridge.notify('logger.log', params);
    },
  },
  ui: {
    showToast: (params: { message: string; duration: 'short' | 'long' }): void => {
      bridge.notify('ui.showToast', params);
    },
  },

  // --- Events (对应 ApiContract['events']) ---
  events: {
    network: {
      statusChanged: {
        subscribe: (listener: (data: NetworkStatusEvent) => void): (() => void) => {
          return bridge.subscribe('network.statusChanged', listener);
        },
      },
    },
    scanner: {
      dataReceived: {
        subscribe: (listener: (data: ScannerDataEvent) => void): (() => void) => {
          return bridge.subscribe('scanner.dataReceived', listener);
        },
      },
    },
  },
};
```

### 5.4. 使用方法 (Usage Example)

在 Vue 组件或其他业务逻辑中，只需导入 `nativeApi` 即可享受类型安全带来的所有好处。

```typescript
// file: src/views/SomeComponent.vue

import { nativeApi } from "@/services/nativeApi";
import { onUnmounted } from "vue";

// --- 示例1: 调用并等待返回 ---
async function handlePrint() {
  try {
    // 直接调用方法，参数类型会被自动补全和检查
    const response = await nativeApi.printer.print({ content: { ticket: "..." } });
    
    // `response` 的类型被自动推断为: ApiResponse<{ jobId: string }>
    if (response.success) {
      // `response.data` 的类型是: { jobId: string }
      console.log("打印成功，任务ID:", response.data.jobId);
    } else {
      // `response.code` 和 `response.message` 存在
      console.error(`打印业务失败 [${response.code}]:`, response.message);
    }
  } catch (error) {
    // `error` 是 JsonRpcErrorResponse['error']，通常是通信层错误（如超时）
    console.error("打印RPC调用失败:", error);
  }
}

// --- 示例2: 发送通知 ---
nativeApi.logger.log({ level: "info", message: "User opened print dialog" });

// --- 示例3: 订阅事件 ---
// `params` 的类型被自动推断为: ScannerDataEvent
const unsubscribeScanner = nativeApi.events.scanner.dataReceived.subscribe((params) => {
  console.log("收到扫码枪数据:", params.code);
});

// 组件卸载时，务必取消订阅以防止内存泄漏
onUnmounted(() => {
  unsubscribeScanner();
});
```

### 5.5. 安全考量 (Security Considerations)

为了确保`JSBridge`的通信安全，防止潜在的恶意脚本攻击，必须在 **原生层** 实施严格的安全策略：

1.  **方法白名单 (Method Whitelist)**:

    -   **描述**: 原生（C#/Java）层应维护一个明确的、可被 Web 调用的方法列表（白名单）。
    -   **实现**: 当接收到来自 Web 的 RPC 请求时，首先检查其 `method` 字段是否位于白名单内。如果不在，应立即拒绝该请求并记录安全警告，而不是尝试执行未知方法。
    -   **理由**: 这是最核心的安全机制，从根本上杜绝了攻击者调用未授权或危险的原生 API 的可能性。

2.  **参数强校验 (Strong Parameter Validation)**:

    -   **描述**: 对于白名单中的每一个方法，原生层都必须对其接收到的 `params` 进行严格的校验。
    -   **实现**: 校验应包括类型、格式、范围和存在性检查。
    -   **理由**: 防止格式错误的、恶意的或越界的参数导致原生代码抛出未处理的异常，甚至引发更严重的安全问题（如路径遍历、SQL注入等）。

3.  **来源验证 (Origin Validation)**:
    -   **描述**: 在 WebView 初始化时，验证其加载的内容来源是否可信。
    -   **实现**: 仅允许加载打包在应用内的本地 `html` 文件，或限定加载指定的、受信任的远程 HTTPS 域。在 C# 中，可以通过监听 `CoreWebView2.NavigationStarting` 事件并检查 `args.Uri` 属性来实现。
    -   **理由**: 防止应用被引导去加载一个恶意的第三方网页，该网页中的脚本可能会尝试利用 JSBridge 攻击原生应用。

## 6. 响应式布局方案

### 6.1. 多分辨率适配策略

#### 6.1.1. CSS 变量动态缩放

```scss
// styles/responsive.scss
:root {
  // 基础尺寸
  --base-font-size: 16px;
  --base-spacing: 20px;
  --base-button-height: 48px;
  --base-input-height: 40px;

  // 缩放后的尺寸
  --font-size: calc(var(--base-font-size) * var(--scale));
  --spacing: calc(var(--base-spacing) * var(--scale));
  --button-height: calc(var(--base-button-height) * var(--scale));
  --input-height: calc(var(--base-input-height) * var(--scale));

  // 断点定义
  --breakpoint-sm: 1024px;
  --breakpoint-md: 1366px;
  --breakpoint-lg: 1920px;
}

// 应用缩放
.responsive-container {
  font-size: var(--font-size);

  .button {
    height: var(--button-height);
    padding: calc(var(--spacing) / 2) var(--spacing);
    border-radius: calc(var(--spacing) / 4);
  }

  .input {
    height: var(--input-height);
    padding: 0 calc(var(--spacing) / 2);
  }

  .spacing {
    margin: var(--spacing);
    padding: var(--spacing);
  }
}
```

#### 6.1.2. Vue 组合式函数：屏幕适配

```typescript
// composables/useResponsive.ts
import { ref, onMounted, onUnmounted, computed } from "vue";

export function useResponsive() {
  const screenWidth = ref(window.innerWidth);
  const screenHeight = ref(window.innerHeight);
  const scale = ref(1);

  // 屏幕类型
  const screenType = computed(() => {
    if (screenWidth.value <= 1024) return "small";
    if (screenWidth.value <= 1366) return "medium";
    if (screenWidth.value <= 1920) return "large";
    return "xlarge";
  });

  // 是否为竖屏
  const isPortrait = computed(() => screenHeight.value > screenWidth.value);

  // 计算缩放比例
  const calculateScale = () => {
    const baseWidth = 1920;
    const baseHeight = 1080;

    const scaleX = screenWidth.value / baseWidth;
    const scaleY = screenHeight.value / baseHeight;

    scale.value = Math.min(scaleX, scaleY);

    // 设置CSS变量
    document.documentElement.style.setProperty(
      "--scale",
      scale.value.toString()
    );
  };

  // 响应窗口大小变化
  const handleResize = () => {
    screenWidth.value = window.innerWidth;
    screenHeight.value = window.innerHeight;
    calculateScale();
  };

  // 获取响应式尺寸
  const getResponsiveSize = (baseSize: number): number => {
    return Math.round(baseSize * scale.value);
  };

  // 获取响应式样式
  const getResponsiveStyle = (styles: Record<string, number>) => {
    const result: Record<string, string> = {};

    for (const [key, value] of Object.entries(styles)) {
      result[key] = `${getResponsiveSize(value)}px`;
    }

    return result;
  };

  onMounted(() => {
    calculateScale();
    window.addEventListener("resize", handleResize);
  });

  onUnmounted(() => {
    window.removeEventListener("resize", handleResize);
  });

  return {
    screenWidth,
    screenHeight,
    screenType,
    isPortrait,
    scale,
    getResponsiveSize,
    getResponsiveStyle
  };
}
```

## 7. 自动更新设计 (Auto-Update Design)

为了实现灵活、可控的软件版本迭代，自动更新机制将支持全量、灰度两种发布模式。该设计旨在确保更新过程的稳定性、安全性与用户体验。

### 7.1. 核心目标

- **可控发布**: 管理员能够精确控制更新范围，支持设备ID或用户百分比进行灰度发布。
- **无缝体验**: 更新过程在后台自动进行，对用户的干扰降至最低。
- **安全可靠**: 所有更新包都需经过签名验证，防止恶意篡改；更新失败时应有回滚机制。
- **前后端分离**: 原生应用（C#）和Web前端（Vue）可独立或捆绑更新。

### 7.2. 更新架构

更新系统由 **更新服务器** 和 **客户端更新模块** 两部分组成。

- **更新服务器**: 
  - 存储所有版本的更新包（全量包与增量包）。
  - 提供API供客户端检查更新。
  - 托管更新策略配置文件，定义发布规则。
- **客户端更新模块 (集成在C#原生应用中)**:
  - 定期向更新服务器发起检查请求。
  - 上报自身标识信息（设备ID、当前版本号）。
  - 根据服务器返回的策略，下载并应用更新。
  - 负责更新原生应用及内置的Web资源。

### 7.3. 更新策略配置

更新策略由部署在服务器的 `update-policy.json` 文件定义，它允许管理员灵活配置发布规则。

#### 7.3.1. 配置文件结构

```json
{
  "schemaVersion": "1.0",
  "releases": [
    {
      "version": "2.1.0",
      "releaseDate": "2023-10-27T10:00:00Z",
      "notes": "- 新增会员积分功能\n- 优化订单结算速度",
      "packages": {
        "native": {
          "url": "https://update.server.com/v2.1.0/native.zip",
          "sha256": "a1b2c3d4..."
        },
        "web": {
          "url": "https://update.server.com/v2.1.0/web.zip",
          "sha256": "e5f6g7h8..."
        }
      },
      "strategy": {
        "type": "grayscale", // "full" 或 "grayscale"
        "rules": [
          { "by": "deviceId", "values": ["D0-A1-B2-C3-D4-E5"] },
          { "by": "percentage", "value": 10 }
        ]
      }
    },
    {
      "version": "2.0.0",
      "releaseDate": "2023-10-20T18:00:00Z",
      "notes": "- 全新的UI设计\n- 支持多种支付方式",
      "packages": { /* ... */ },
      "strategy": {
        "type": "full"
      }
    }
  ]
}
```

#### 7.3.2. 规则详解

- **`releases`**: 一个数组，包含所有可用的发行版本，客户端将从上到下匹配。
- **`version`**: 遵循语义化版本号（`Major.Minor.Patch`）。
- **`packages`**: 包含 `native` 和 `web` 两部分的更新包信息。
  - `url`: 更新包下载地址。
  - `sha256`: 文件哈希，用于校验完整性。
- **`strategy.type`**: 
  - `full`: 全量更新，所有请求更新的客户端都会收到此版本。
  - `grayscale`: 灰度更新，只有满足 `rules` 中至少一条规则的客户端才会收到此版本。
- **`strategy.rules`** (仅在 `type` 为 `grayscale` 时有效):
  - `by`: 规则类型，可以是 `deviceId` (设备唯一标识), 或 `percentage` (用户百分比)。
  - `values`/`value`: 规则的值。对于 `percentage`，客户端可以通过 `hash(deviceId) % 100 < value` 来判断是否命中。

### 7.4. 更新流程

```mermaid
sequenceDiagram
    participant Client as POS客户端 (C#)
    participant Server as 更新服务器

    Client->>Server: 1. 检查更新请求 (携带 storeId, deviceId, currentVersion)
    Server->>Server: 2. 加载 update-policy.json
    Server->>Server: 3. 遍历 releases，匹配客户端版本和策略
    alt 找到匹配的更新
        Server-->>Client: 4a. 响应更新信息 (newVersion, notes, packageUrls, hashes)
        Client->>Client: 5. 在后台下载更新包
        Client->>Client: 6. 校验文件哈希 (SHA256)
        Client->>Client: 7. 解压并替换旧文件 (失败则回滚)
        Client->>Client: 8. 提示用户重启以应用更新
    else 未找到更新
        Server-->>Client: 4b. 响应无可用更新
    end
```

### 7.5. 安全与回滚

- **签名验证**: 所有下发的更新包（尤其是原生执行文件）必须经过代码签名。客户端在应用更新前必须验证签名，确保来源可信、未被篡改。
- **原子操作与回滚**: 更新过程应是原子的。推荐采用“蓝绿部署”模式：将新版本下载到新的目录，验证成功后，通过修改一个指向当前版本的符号链接或配置文件来切换版本。如果新版本启动失败，只需将指针指回旧版本即可快速回滚。

## 8. 日志系统 (Logging System)

一个分层、可远程调试的日志系统至关重要。

- **原生日志核心 (C#):**

  - 使用 `Serilog` 或 `NLog` 框架。
  - 配置多个输出目标（Sinks）：
    - **File Sink:** 在本地按日期滚动记录所有级别的日志文件，便于排查。
    - **Remote Sink (可选):** 将 `WARN` 和 `ERROR` 级别以上的日志实时上报到 Sentry、ELK 等远程日志分析平台。
  - 通过 JSBridge 暴露接口给 Web，用于接收并记录来自 Web 端的日志（支持单条和批量）。

- **Web 端日志 (Vue):**
  - 封装 `logger.ts` 服务，该服务负责日志的收集与上报。
  - 通过 `app.config.errorHandler` 和 `window.addEventListener('unhandledrejection', ...)` 全局捕获 Vue 组件异常和 Promise 异常。
  - **上报策略**:
    - **实时上报**: `ERROR` 级别的严重错误（如未捕获的异常）会立即调用 `nativeApi.logger.log` 发送给原生层。
    - **批量上报**: `INFO`、`WARN` 等普通级别的日志会先暂存在内存队列中。**每小时**通过定时器 (`setInterval`) 触发，或当日志数量达到阈值时，将队列中的日志打包，调用 `JSBridge` 接口一次性发送给原生层进行归档，发送成功后清空队列。
  - **日志存储**: 日志主要由原生层负责持久化，Web 端只做内存缓存，不直接操作文件系统或 `localStorage`，以避免性能开销和存储限制。

## 9. 用户行为分析 (Baidu Tongji)

为了解用户行为，我们集成百度统计。

1.  **引入脚本:** 在 `index.html` 中引入百度统计的基础 JS 代码。
2.  **PV 上报:** 在 `vue-router` 的全局后置守卫 `router.afterEach` 中，手动调用 `window._hmt.push(['_trackPageview', to.fullPath]);` 来上报页面浏览, 或开启单页应用。
3.  **事件上报:** 封装一个 `trackEvent(category, action, ...)` 工具函数，在关键的用户操作点（如点击支付、添加商品）进行调用，上报自定义事件。

## 10. 项目结构设计 (Project Structure)

一个清晰、可扩展的项目结构是高效协作和长期维护的基石。我们采用功能模块化的方式组织代码，确保高内聚、低耦合。

### 10.1. C# 原生宿主 (`pos-native`)

原生宿主负责提供稳定的运行环境、硬件访问能力和核心的非 UI 业务逻辑。

```plaintext
/pos-native
|-- /assets                     # 应用图标、原生图片等资源
|-- /bridge                     # 通信桥接层
|   |-- BridgeHandler.cs        # 核心消息处理器，解析RPC请求并分发
|   |-- JsonRpcModels.cs        # JSON-RPC 2.0 协议的数据模型
|   `-- NativeApi.cs            # 暴露给Web的所有原生方法的实现
|-- /core                       # 核心业务逻辑
|   |-- /data                   # 数据访问层 (如果需要本地数据库)
|   `-- /services               # 核心服务，如订单处理、库存管理等
|-- /hardware                   # 硬件抽象层
|   |-- /drivers                # 具体硬件驱动的封装
|   |-- IPrinter.cs             # 打印机接口
|   |-- IScanner.cs             # 扫描枪接口
|   `-- PrinterService.cs       # 打印机服务实现
|-- /platform                   # 平台相关功能
|   |-- AutoUpdater.cs          # 自动更新服务
|   `-- WindowManager.cs        # 窗口管理
|-- /web-app                    # (构建产物) 存放Vue打包后的静态文件
|-- MainWindow.xaml             # 主窗口定义 (WPF)
|-- MainWindow.xaml.cs          # 主窗口后台代码，初始化WebView2
|-- App.xaml                    # 应用定义
|-- App.xaml.cs                 # 应用启动逻辑
|-- Program.cs                  # [可选] 控制台应用程序入口
`-- pos-native.csproj           # 项目文件
```

### 10.2. Vue 前端核心 (`pos-web`)

Web 核心负责所有 UI 展现、用户交互和业务流程的编排。

```plaintext
/pos-web
|-- /public                     # 静态资源，会被直接复制到dist目录
|   |-- favicon.ico
|   `-- index.html              # 应用入口HTML (可注入统计脚本)
|-- /src
|   |-- /api                    # 后端HTTP API请求封装 (axios实例、接口模块)
|   |-- /assets                 # 样式、图片、字体等项目资源
|   |   |-- /fonts
|   |   |-- /icons              # SVG图标
|   |   `-- /styles             # 全局样式、变量、混入 (main.scss, responsive.scss)
|   |-- /bridge                 # 与原生通信的桥接服务
|   |   |-- index.ts            # BridgeService的实现和导出
|   |   `-- types.ts            # JSON-RPC和事件的TypeScript类型定义
|   |-- /components             # 全局通用组件 (原子组件、业务组件)
|   |   |-- /common             # 基础UI组件 (Button.vue, Input.vue, Modal.vue)
|   |   `-- /business           # 业务相关组件 (ProductCard.vue, OrderList.vue)
|   |-- /composables            # Vue组合式函数 (Hooks)
|   |   |-- use-responsive.ts   # 响应式布局逻辑
|   |   `-- use-event-bus.ts    # 事件总线
|   |-- /constants              # 全局常量 (枚举、配置)
|   |-- /layouts                # 布局组件
|   |   |-- DefaultLayout.vue
|   |   `-- CashierLayout.vue
|   |-- /router                 # Vue Router配置
|   |   `-- index.ts
|   |-- /store                  # Pinia状态管理
|   |   |-- /modules            # 按模块拆分 (user.ts, cart.ts, settings.ts)
|   |   `-- index.ts            # 根store配置
|   |-- /types                  # 全局TypeScript类型定义
|   |   `-- index.d.ts
|   |-- /utils                  # 通用工具函数
|   |   |-- format.ts           # 格式化函数
|   |   `-- request.ts          # HTTP请求封装
|   |-- /views                  # 页面级组件 (路由组件)
|   |   |-- /cashier
|   |   |-- /dashboard
|   |   `-- /login
|   |-- App.vue                 # 根组件
|   `-- main.ts                 # 应用入口文件
|-- .editorconfig
|-- .env.development            # 开发环境变量
|-- .env.production             # 生产环境变量
|-- .eslintrc.cjs
|-- .gitignore
|-- package.json
|-- postcss.config.js
|-- tsconfig.json
`-- vite.config.ts
```

## 11. 开发规范与指南 (Development Guide)

为确保代码质量、开发效率和长期可维护性，所有团队成员必须遵守以下规范。

### 11.1. 命名规范

- **目录/文件夹**: 全部使用小写 `kebab-case` (短横线连接)。例如: `src/components/business`, `src/composables`。
- **Vue 组件文件**: 使用 `PascalCase` (大驼峰命名)。例如: `ProductCard.vue`, `BaseModal.vue`。
- **非组件 TS/JS 文件**: 使用 `kebab-case`。例如: `use-responsive.ts`, `api-client.ts`。
- **C# 文件/类/接口**: 使用 `PascalCase`。接口名前加 `I`。例如: `PrinterService.cs`, `IScanner.cs`。
- **变量/函数**: 使用 `camelCase` (小驼峰命名)。例如: `const cartItems = ...`, `function calculateTotal() {}`。
- **CSS 类名**: 使用 `kebab-case`，并可结合 BEM 思想。例如: `.product-card__title`。

### 11.2. Git 工作流与提交规范

- **分支模型**:
  - `main`: 生产分支，只接受来自 `develop` 的合并。
  - `develop`: 开发主分支，集成所有已完成的功能。
  - `feature/xxx`: 功能开发分支，从 `develop` 创建，完成后合并回 `develop`。
  - `fix/xxx`: Bug 修复分支。
  - `refactor/xxx`: 重构分支。
- **提交信息 (Commit Message)**: 遵循 [Git 提交规范](https://alibaba.github.io/f2e-spec/zh/engineering/git/) 规范。
  - 格式: `<type>(<scope>): <subject>`
  - 示例: `feat(cart): add item removal functionality`
  - 常用 `type`: `feat`, `fix`, `docs`, `style`, `refactor`, `test`, `chore`。

### 11.3. 编码规范

- **接口先行**: 所有原生与 Web 的交互，必须先在 `src/bridge/types.ts` 中定义清晰的 TypeScript 接口和事件类型。
- **职责边界**: 严格遵守功能划分。原生只提供“能力”，Web 负责“展现”和“业务编排”。原生接口应是功能性的 (`print`) 而非过程性的 (`openPort`)。
- **Vue 最佳实践**:
  - 优先使用 `<script setup>` 语法。
  - 组合式函数 (`composables`) 应用于封装和复用有状态逻辑。
  - 遵循单向数据流，UI 组件应从 Pinia Store 获取状态，通过 Action 修改状态。
  - 组件 props 定义应尽可能详细，包含 `type`, `required`, `default` 和 `validator`。
  - Vue (核心) [官方风格指南 (必要)](https://vuejs.org/style-guide/rules-essential.html) 
  - Vue (强烈推荐) [官方风格指南 (强烈推荐)](https://vuejs.org/style-guide/rules-strongly-recommended.html) 
  - Vue (推荐) [官方风格指南 (推荐)](https://vuejs.org/style-guide/rules-recommended.html) 
  - Vue (谨慎使用) [官方风格指南 (谨慎使用)](https://vuejs.org/style-guide/rules-use-with-caution.html) 
  - TypeScript [阿里巴巴前端规约](https://alibaba.github.io/f2e-spec/zh/coding/typescript/) 
- **C#最佳实践**:
  - 遵循 SOLID 原则。
  - 使用依赖注入（Dependency Injection）来解耦服务。
  - 异步方法必须使用 `async/await` 并以 `Async` 后缀命名。
- **日志规范**: 关键用户路径、API 调用和错误处理必须记录日志。日志信息需包含充足的上下文（如用户 ID、订单号、错误堆栈）。

### 11.4. 代码审查 (Code Review)

- 所有向 `develop` 和 `main` 分支的合并请求 (Merge Request / Pull Request) 都必须经过至少一位其他团队成员的审查。
- 审查重点: 代码风格、逻辑正确性、可读性、性能、是否符合架构设计。

### 11.5. 拥抱未来

- `bridge` 层中的接口设计应保持平台无关性，为未来可能的 Android 或 Web 端独立部署奠定基础。
