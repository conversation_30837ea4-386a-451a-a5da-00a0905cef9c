<!DOCTYPE html><html><head>
      <title>POS系统前端软件架构设计方案</title>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      
      <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.21/dist/katex.min.css">
      
      
      <script src="https://cdn.jsdelivr.net/npm/mermaid@11.5.0/dist/mermaid.min.js"></script>
      
      
      <style>
      code[class*=language-],pre[class*=language-]{color:#333;background:0 0;font-family:Consolas,"Liberation Mono",Menlo,Courier,monospace;text-align:left;white-space:pre;word-spacing:normal;word-break:normal;word-wrap:normal;line-height:1.4;-moz-tab-size:8;-o-tab-size:8;tab-size:8;-webkit-hyphens:none;-moz-hyphens:none;-ms-hyphens:none;hyphens:none}pre[class*=language-]{padding:.8em;overflow:auto;border-radius:3px;background:#f5f5f5}:not(pre)>code[class*=language-]{padding:.1em;border-radius:.3em;white-space:normal;background:#f5f5f5}.token.blockquote,.token.comment{color:#969896}.token.cdata{color:#183691}.token.doctype,.token.macro.property,.token.punctuation,.token.variable{color:#333}.token.builtin,.token.important,.token.keyword,.token.operator,.token.rule{color:#a71d5d}.token.attr-value,.token.regex,.token.string,.token.url{color:#183691}.token.atrule,.token.boolean,.token.code,.token.command,.token.constant,.token.entity,.token.number,.token.property,.token.symbol{color:#0086b3}.token.prolog,.token.selector,.token.tag{color:#63a35c}.token.attr-name,.token.class,.token.class-name,.token.function,.token.id,.token.namespace,.token.pseudo-class,.token.pseudo-element,.token.url-reference .token.variable{color:#795da3}.token.entity{cursor:help}.token.title,.token.title .token.punctuation{font-weight:700;color:#1d3e81}.token.list{color:#ed6a43}.token.inserted{background-color:#eaffea;color:#55a532}.token.deleted{background-color:#ffecec;color:#bd2c00}.token.bold{font-weight:700}.token.italic{font-style:italic}.language-json .token.property{color:#183691}.language-markup .token.tag .token.punctuation{color:#333}.language-css .token.function,code.language-css{color:#0086b3}.language-yaml .token.atrule{color:#63a35c}code.language-yaml{color:#183691}.language-ruby .token.function{color:#333}.language-markdown .token.url{color:#795da3}.language-makefile .token.symbol{color:#795da3}.language-makefile .token.variable{color:#183691}.language-makefile .token.builtin{color:#0086b3}.language-bash .token.keyword{color:#0086b3}pre[data-line]{position:relative;padding:1em 0 1em 3em}pre[data-line] .line-highlight-wrapper{position:absolute;top:0;left:0;background-color:transparent;display:block;width:100%}pre[data-line] .line-highlight{position:absolute;left:0;right:0;padding:inherit 0;margin-top:1em;background:hsla(24,20%,50%,.08);background:linear-gradient(to right,hsla(24,20%,50%,.1) 70%,hsla(24,20%,50%,0));pointer-events:none;line-height:inherit;white-space:pre}pre[data-line] .line-highlight:before,pre[data-line] .line-highlight[data-end]:after{content:attr(data-start);position:absolute;top:.4em;left:.6em;min-width:1em;padding:0 .5em;background-color:hsla(24,20%,50%,.4);color:#f4f1ef;font:bold 65%/1.5 sans-serif;text-align:center;vertical-align:.3em;border-radius:999px;text-shadow:none;box-shadow:0 1px #fff}pre[data-line] .line-highlight[data-end]:after{content:attr(data-end);top:auto;bottom:.4em}html body{font-family:'Helvetica Neue',Helvetica,'Segoe UI',Arial,freesans,sans-serif;font-size:16px;line-height:1.6;color:#333;background-color:#fff;overflow:initial;box-sizing:border-box;word-wrap:break-word}html body>:first-child{margin-top:0}html body h1,html body h2,html body h3,html body h4,html body h5,html body h6{line-height:1.2;margin-top:1em;margin-bottom:16px;color:#000}html body h1{font-size:2.25em;font-weight:300;padding-bottom:.3em}html body h2{font-size:1.75em;font-weight:400;padding-bottom:.3em}html body h3{font-size:1.5em;font-weight:500}html body h4{font-size:1.25em;font-weight:600}html body h5{font-size:1.1em;font-weight:600}html body h6{font-size:1em;font-weight:600}html body h1,html body h2,html body h3,html body h4,html body h5{font-weight:600}html body h5{font-size:1em}html body h6{color:#5c5c5c}html body strong{color:#000}html body del{color:#5c5c5c}html body a:not([href]){color:inherit;text-decoration:none}html body a{color:#08c;text-decoration:none}html body a:hover{color:#00a3f5;text-decoration:none}html body img{max-width:100%}html body>p{margin-top:0;margin-bottom:16px;word-wrap:break-word}html body>ol,html body>ul{margin-bottom:16px}html body ol,html body ul{padding-left:2em}html body ol.no-list,html body ul.no-list{padding:0;list-style-type:none}html body ol ol,html body ol ul,html body ul ol,html body ul ul{margin-top:0;margin-bottom:0}html body li{margin-bottom:0}html body li.task-list-item{list-style:none}html body li>p{margin-top:0;margin-bottom:0}html body .task-list-item-checkbox{margin:0 .2em .25em -1.8em;vertical-align:middle}html body .task-list-item-checkbox:hover{cursor:pointer}html body blockquote{margin:16px 0;font-size:inherit;padding:0 15px;color:#5c5c5c;background-color:#f0f0f0;border-left:4px solid #d6d6d6}html body blockquote>:first-child{margin-top:0}html body blockquote>:last-child{margin-bottom:0}html body hr{height:4px;margin:32px 0;background-color:#d6d6d6;border:0 none}html body table{margin:10px 0 15px 0;border-collapse:collapse;border-spacing:0;display:block;width:100%;overflow:auto;word-break:normal;word-break:keep-all}html body table th{font-weight:700;color:#000}html body table td,html body table th{border:1px solid #d6d6d6;padding:6px 13px}html body dl{padding:0}html body dl dt{padding:0;margin-top:16px;font-size:1em;font-style:italic;font-weight:700}html body dl dd{padding:0 16px;margin-bottom:16px}html body code{font-family:Menlo,Monaco,Consolas,'Courier New',monospace;font-size:.85em;color:#000;background-color:#f0f0f0;border-radius:3px;padding:.2em 0}html body code::after,html body code::before{letter-spacing:-.2em;content:'\00a0'}html body pre>code{padding:0;margin:0;word-break:normal;white-space:pre;background:0 0;border:0}html body .highlight{margin-bottom:16px}html body .highlight pre,html body pre{padding:1em;overflow:auto;line-height:1.45;border:#d6d6d6;border-radius:3px}html body .highlight pre{margin-bottom:0;word-break:normal}html body pre code,html body pre tt{display:inline;max-width:initial;padding:0;margin:0;overflow:initial;line-height:inherit;word-wrap:normal;background-color:transparent;border:0}html body pre code:after,html body pre code:before,html body pre tt:after,html body pre tt:before{content:normal}html body blockquote,html body dl,html body ol,html body p,html body pre,html body ul{margin-top:0;margin-bottom:16px}html body kbd{color:#000;border:1px solid #d6d6d6;border-bottom:2px solid #c7c7c7;padding:2px 4px;background-color:#f0f0f0;border-radius:3px}@media print{html body{background-color:#fff}html body h1,html body h2,html body h3,html body h4,html body h5,html body h6{color:#000;page-break-after:avoid}html body blockquote{color:#5c5c5c}html body pre{page-break-inside:avoid}html body table{display:table}html body img{display:block;max-width:100%;max-height:100%}html body code,html body pre{word-wrap:break-word;white-space:pre}}.markdown-preview{width:100%;height:100%;box-sizing:border-box}.markdown-preview ul{list-style:disc}.markdown-preview ul ul{list-style:circle}.markdown-preview ul ul ul{list-style:square}.markdown-preview ol{list-style:decimal}.markdown-preview ol ol,.markdown-preview ul ol{list-style-type:lower-roman}.markdown-preview ol ol ol,.markdown-preview ol ul ol,.markdown-preview ul ol ol,.markdown-preview ul ul ol{list-style-type:lower-alpha}.markdown-preview .newpage,.markdown-preview .pagebreak{page-break-before:always}.markdown-preview pre.line-numbers{position:relative;padding-left:3.8em;counter-reset:linenumber}.markdown-preview pre.line-numbers>code{position:relative}.markdown-preview pre.line-numbers .line-numbers-rows{position:absolute;pointer-events:none;top:1em;font-size:100%;left:0;width:3em;letter-spacing:-1px;border-right:1px solid #999;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.markdown-preview pre.line-numbers .line-numbers-rows>span{pointer-events:none;display:block;counter-increment:linenumber}.markdown-preview pre.line-numbers .line-numbers-rows>span:before{content:counter(linenumber);color:#999;display:block;padding-right:.8em;text-align:right}.markdown-preview .mathjax-exps .MathJax_Display{text-align:center!important}.markdown-preview:not([data-for=preview]) .code-chunk .code-chunk-btn-group{display:none}.markdown-preview:not([data-for=preview]) .code-chunk .status{display:none}.markdown-preview:not([data-for=preview]) .code-chunk .output-div{margin-bottom:16px}.markdown-preview .md-toc{padding:0}.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link{display:inline;padding:.25rem 0}.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link div,.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link p{display:inline}.markdown-preview .md-toc .md-toc-link-wrapper.highlighted .md-toc-link{font-weight:800}.scrollbar-style::-webkit-scrollbar{width:8px}.scrollbar-style::-webkit-scrollbar-track{border-radius:10px;background-color:transparent}.scrollbar-style::-webkit-scrollbar-thumb{border-radius:5px;background-color:rgba(150,150,150,.66);border:4px solid rgba(150,150,150,.66);background-clip:content-box}html body[for=html-export]:not([data-presentation-mode]){position:relative;width:100%;height:100%;top:0;left:0;margin:0;padding:0;overflow:auto}html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{position:relative;top:0;min-height:100vh}@media screen and (min-width:914px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{padding:2em calc(50% - 457px + 2em)}}@media screen and (max-width:914px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{padding:2em}}@media screen and (max-width:450px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{font-size:14px!important;padding:1em}}@media print{html body[for=html-export]:not([data-presentation-mode]) #sidebar-toc-btn{display:none}}html body[for=html-export]:not([data-presentation-mode]) #sidebar-toc-btn{position:fixed;bottom:8px;left:8px;font-size:28px;cursor:pointer;color:inherit;z-index:99;width:32px;text-align:center;opacity:.4}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] #sidebar-toc-btn{opacity:1}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc{position:fixed;top:0;left:0;width:300px;height:100%;padding:32px 0 48px 0;font-size:14px;box-shadow:0 0 4px rgba(150,150,150,.33);box-sizing:border-box;overflow:auto;background-color:inherit}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar{width:8px}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar-track{border-radius:10px;background-color:transparent}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar-thumb{border-radius:5px;background-color:rgba(150,150,150,.66);border:4px solid rgba(150,150,150,.66);background-clip:content-box}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc a{text-decoration:none}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc{padding:0 16px}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link{display:inline;padding:.25rem 0}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link div,html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link p{display:inline}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper.highlighted .md-toc-link{font-weight:800}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{left:300px;width:calc(100% - 300px);padding:2em calc(50% - 457px - 300px / 2);margin:0;box-sizing:border-box}@media screen and (max-width:1274px){html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{padding:2em}}@media screen and (max-width:450px){html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{width:100%}}html body[for=html-export]:not([data-presentation-mode]):not([html-show-sidebar-toc]) .markdown-preview{left:50%;transform:translateX(-50%)}html body[for=html-export]:not([data-presentation-mode]):not([html-show-sidebar-toc]) .md-sidebar-toc{display:none}
/* Please visit the URL below for more information: */
/*   https://shd101wyy.github.io/markdown-preview-enhanced/#/customize-css */

      </style>
      <!-- The content below will be included at the end of the <head> element. --><script type="text/javascript">
  document.addEventListener("DOMContentLoaded", function () {
    // your code here
  });
</script></head><body for="html-export">
    
    
      <div class="crossnote markdown-preview  ">
      
<h1 id="pos-系统前端软件架构设计方案">POS 系统前端软件架构设计方案 </h1>
<p><strong>目录</strong></p>
<ul>
<li><a href="#pos-%E7%B3%BB%E7%BB%9F%E5%89%8D%E7%AB%AF%E8%BD%AF%E4%BB%B6%E6%9E%B6%E6%9E%84%E8%AE%BE%E8%AE%A1%E6%96%B9%E6%A1%88">POS 系统前端软件架构设计方案</a>
<ul>
<li><a href="#1-%E6%95%B4%E4%BD%93%E6%9E%B6%E6%9E%84-overall-architecture">1. 整体架构 (Overall Architecture)</a></li>
<li><a href="#2-%E5%8A%9F%E8%83%BD%E5%88%92%E5%88%86%E5%8E%9F%E7%94%9F-vs-web">2. 功能划分：原生 vs. Web</a>
<ul>
<li><a href="#21-%E5%8E%9F%E7%94%9F-c-%E5%AE%9E%E7%8E%B0%E7%9A%84%E5%8A%9F%E8%83%BD">2.1. 原生 (C#) 实现的功能</a>
<ul>
<li><a href="#211-%E5%A4%96%E8%AE%BE%E8%BE%93%E5%85%A5%E7%9A%84%E6%8D%95%E8%8E%B7%E4%B8%8E%E5%A4%84%E7%90%86">2.1.1. 外设输入的捕获与处理</a></li>
<li><a href="#%E6%89%AB%E7%A0%81%E6%9E%AA">扫码枪</a></li>
<li><a href="#%E5%BB%B6%E8%BF%9F">延迟</a></li>
<li><a href="#%E6%9C%80%E4%BD%B3%E5%AE%9E%E8%B7%B5%E5%9C%A8%E5%8E%9F%E7%94%9F%E5%B1%82%E8%BF%9B%E8%A1%8C%E8%BE%93%E5%85%A5%E6%8B%A6%E6%88%AA">最佳实践：在原生层进行输入拦截</a></li>
<li><a href="#212-%E8%BF%9B%E7%A8%8B%E5%B4%A9%E6%BA%83%E4%B8%8E%E6%81%A2%E5%A4%8D%E4%BD%BF%E7%94%A8-processfailed-%E4%BA%8B%E4%BB%B6">2.1.2. 进程崩溃与恢复：使用 ProcessFailed 事件</a></li>
<li><a href="#213-webview2-runtime-%E7%9A%84%E5%AE%89%E8%A3%85%E4%B8%8E%E9%83%A8%E7%BD%B2%E7%AD%96%E7%95%A5">2.1.3. WebView2 Runtime 的安装与部署策略</a></li>
</ul>
</li>
<li><a href="#22-web-vue-3-%E5%AE%9E%E7%8E%B0%E7%9A%84%E5%8A%9F%E8%83%BD">2.2. Web (Vue 3) 实现的功能</a></li>
</ul>
</li>
<li><a href="#3-%E6%8A%80%E6%9C%AF%E6%A0%88-tech-stack">3. 技术栈 (Tech Stack)</a></li>
<li><a href="#4-%E6%95%B0%E6%8D%AE%E6%B5%81%E5%90%91%E8%AE%BE%E8%AE%A1-data-flow">4. 数据流向设计 (Data Flow)</a>
<ul>
<li><a href="#41-%E5%9C%BA%E6%99%AF%E4%B8%80%E7%94%A8%E6%88%B7%E7%82%B9%E5%87%BB%E6%89%93%E5%8D%B0%E5%B0%8F%E7%A5%A8">4.1. 场景一：用户点击“打印小票”</a></li>
<li><a href="#42-%E5%9C%BA%E6%99%AF%E4%BA%8C%E5%BA%94%E7%94%A8%E5%90%AF%E5%8A%A8%E5%8A%A0%E8%BD%BD%E5%95%86%E5%93%81%E6%95%B0%E6%8D%AE">4.2. 场景二：应用启动加载商品数据</a></li>
<li><a href="#43-%E5%9C%BA%E6%99%AF%E4%B8%89%E6%89%AB%E6%8F%8F%E6%9E%AA%E6%89%AB%E6%8F%8F%E5%95%86%E5%93%81%E6%9D%A1%E7%A0%81">4.3. 场景三：扫描枪扫描商品条码</a></li>
</ul>
</li>
<li><a href="#5-%E9%80%9A%E4%BF%A1%E8%A7%84%E8%8C%83-communication-protocol">5. 通信规范 (Communication Protocol)</a>
<ul>
<li><a href="#51-%E6%A0%B8%E5%BF%83%E8%AE%BE%E8%AE%A1%E5%8E%9F%E5%88%99">5.1. 核心设计原则</a></li>
<li><a href="#52-%E5%8D%8F%E8%AE%AE%E4%B8%8E%E6%A0%B8%E5%BF%83%E6%95%B0%E6%8D%AE%E7%BB%93%E6%9E%84">5.2. 协议与核心数据结构</a>
<ul>
<li><a href="#521-%E5%BA%95%E5%B1%82%E5%8D%8F%E8%AE%AE-json-rpc-20">5.2.1. 底层协议: JSON-RPC 2.0</a></li>
<li><a href="#522-%E6%A0%87%E5%87%86%E5%8C%96%E4%B8%9A%E5%8A%A1%E5%93%8D%E5%BA%94-apiresponset">5.2.2. 标准化业务响应: <code>ApiResponse&lt;T&gt;</code></a></li>
</ul>
</li>
<li><a href="#53-%E5%AE%9E%E7%8E%B0%E6%96%B9%E6%A1%88%E7%B1%BB%E5%9E%8B%E5%AE%89%E5%85%A8%E7%9A%84-bridge">5.3. 实现方案：类型安全的 Bridge</a>
<ul>
<li><a href="#531-%E6%AD%A5%E9%AA%A4%E4%B8%80-%E5%AE%9A%E4%B9%89-api-%E5%A5%91%E7%BA%A6-srcbridgetypests">5.3.1. 步骤一: 定义 API 契约 (<code>src/bridge/types.ts</code>)</a></li>
<li><a href="#532-%E6%AD%A5%E9%AA%A4%E4%BA%8C-%E5%B0%81%E8%A3%85%E5%BA%95%E5%B1%82%E9%80%9A%E4%BF%A1%E6%9C%8D%E5%8A%A1-srcservicesbridgets">5.3.2. 步骤二: 封装底层通信服务 (<code>src/services/bridge.ts</code>)</a></li>
<li><a href="#533-%E6%AD%A5%E9%AA%A4%E4%B8%89-%E5%88%9B%E5%BB%BA%E5%BC%BA%E7%B1%BB%E5%9E%8B-api-%E6%8E%A5%E5%8F%A3-srcservicesnativeapits">5.3.3. 步骤三: 创建强类型 API 接口 (<code>src/services/nativeApi.ts</code>)</a></li>
</ul>
</li>
<li><a href="#54-%E4%BD%BF%E7%94%A8%E6%96%B9%E6%B3%95-usage-example">5.4. 使用方法 (Usage Example)</a></li>
<li><a href="#55-%E5%AE%89%E5%85%A8%E8%80%83%E9%87%8F-security-considerations">5.5. 安全考量 (Security Considerations)</a></li>
</ul>
</li>
<li><a href="#6-%E5%93%8D%E5%BA%94%E5%BC%8F%E5%B8%83%E5%B1%80%E6%96%B9%E6%A1%88">6. 响应式布局方案</a>
<ul>
<li><a href="#61-%E5%A4%9A%E5%88%86%E8%BE%A8%E7%8E%87%E9%80%82%E9%85%8D%E7%AD%96%E7%95%A5">6.1. 多分辨率适配策略</a>
<ul>
<li><a href="#611-css-%E5%8F%98%E9%87%8F%E5%8A%A8%E6%80%81%E7%BC%A9%E6%94%BE">6.1.1. CSS 变量动态缩放</a></li>
<li><a href="#612-vue-%E7%BB%84%E5%90%88%E5%BC%8F%E5%87%BD%E6%95%B0%E5%B1%8F%E5%B9%95%E9%80%82%E9%85%8D">6.1.2. Vue 组合式函数：屏幕适配</a></li>
</ul>
</li>
</ul>
</li>
<li><a href="#7-%E8%87%AA%E5%8A%A8%E6%9B%B4%E6%96%B0%E8%AE%BE%E8%AE%A1-auto-update-design">7. 自动更新设计 (Auto-Update Design)</a>
<ul>
<li><a href="#71-%E6%A0%B8%E5%BF%83%E7%9B%AE%E6%A0%87">7.1. 核心目标</a></li>
<li><a href="#72-%E6%9B%B4%E6%96%B0%E6%9E%B6%E6%9E%84">7.2. 更新架构</a></li>
<li><a href="#73-%E6%9B%B4%E6%96%B0%E7%AD%96%E7%95%A5%E9%85%8D%E7%BD%AE">7.3. 更新策略配置</a>
<ul>
<li><a href="#731-%E9%85%8D%E7%BD%AE%E6%96%87%E4%BB%B6%E7%BB%93%E6%9E%84">7.3.1. 配置文件结构</a></li>
<li><a href="#732-%E8%A7%84%E5%88%99%E8%AF%A6%E8%A7%A3">7.3.2. 规则详解</a></li>
</ul>
</li>
<li><a href="#74-%E6%9B%B4%E6%96%B0%E6%B5%81%E7%A8%8B">7.4. 更新流程</a></li>
<li><a href="#75-%E5%AE%89%E5%85%A8%E4%B8%8E%E5%9B%9E%E6%BB%9A">7.5. 安全与回滚</a></li>
</ul>
</li>
<li><a href="#8-%E6%97%A5%E5%BF%97%E7%B3%BB%E7%BB%9F-logging-system">8. 日志系统 (Logging System)</a></li>
<li><a href="#9-%E7%94%A8%E6%88%B7%E8%A1%8C%E4%B8%BA%E5%88%86%E6%9E%90-baidu-tongji">9. 用户行为分析 (Baidu Tongji)</a></li>
<li><a href="#10-%E9%A1%B9%E7%9B%AE%E7%BB%93%E6%9E%84%E8%AE%BE%E8%AE%A1-project-structure">10. 项目结构设计 (Project Structure)</a>
<ul>
<li><a href="#101-c-%E5%8E%9F%E7%94%9F%E5%AE%BF%E4%B8%BB-pos-native">10.1. C# 原生宿主 (<code>pos-native</code>)</a></li>
<li><a href="#102-vue-%E5%89%8D%E7%AB%AF%E6%A0%B8%E5%BF%83-pos-web">10.2. Vue 前端核心 (<code>pos-web</code>)</a></li>
</ul>
</li>
<li><a href="#11-%E5%BC%80%E5%8F%91%E8%A7%84%E8%8C%83%E4%B8%8E%E6%8C%87%E5%8D%97-development-guide">11. 开发规范与指南 (Development Guide)</a>
<ul>
<li><a href="#111-%E5%91%BD%E5%90%8D%E8%A7%84%E8%8C%83">11.1. 命名规范</a></li>
<li><a href="#112-git-%E5%B7%A5%E4%BD%9C%E6%B5%81%E4%B8%8E%E6%8F%90%E4%BA%A4%E8%A7%84%E8%8C%83">11.2. Git 工作流与提交规范</a></li>
<li><a href="#113-%E7%BC%96%E7%A0%81%E8%A7%84%E8%8C%83">11.3. 编码规范</a></li>
<li><a href="#114-%E4%BB%A3%E7%A0%81%E5%AE%A1%E6%9F%A5-code-review">11.4. 代码审查 (Code Review)</a></li>
<li><a href="#115-%E6%8B%A5%E6%8A%B1%E6%9C%AA%E6%9D%A5">11.5. 拥抱未来</a></li>
</ul>
</li>
</ul>
</li>
</ul>
<h2 id="1-整体架构-overall-architecture">1. 整体架构 (Overall Architecture) </h2>
<p>本方案采用业界成熟的 <strong>原生容器与 Web 核心相结合</strong> 的混合式架构（Hybrid Architecture），旨在融合原生技术与 Web 技术的各自优势，以实现开发效率、用户体验与系统性能的最佳平衡。</p>
<ul>
<li>
<p><strong>核心思想</strong>: 采用轻量级原生应用（C#/.NET）作为宿主容器，承载核心的 Web 应用（Vue 3）。此模式下，原生层专注于处理底层系统交互与硬件集成等平台相关任务，而 Web 层则负责所有业务逻辑实现与用户界面（UI）的构建，从而实现开发敏捷性。</p>
</li>
<li>
<p><strong>架构目标</strong>:</p>
<ul>
<li><strong>高效开发</strong>: 充分利用 Web 前端生态系统，实现 UI 和业务逻辑的快速开发与迭代。</li>
<li><strong>卓越体验</strong>: 借助原生能力，提供流畅的硬件交互和无缝的系统集成体验。</li>
<li><strong>平台兼容</strong>: Web 核心确保 UI 与业务逻辑具备高度可移植性，能够平滑迁移至未来的 Android 或其他平台。</li>
<li><strong>稳定可靠</strong>: 原生容器为应用提供稳健的运行环境，并实现对系统资源的有效管理。</li>
</ul>
</li>
<li>
<p><strong>分层职责</strong>:</p>
<ul>
<li>
<p><strong>原生容器 (Native Container - C#/.NET):</strong> 作为应用的<strong>宿主环境 (Hosting Environment)</strong>，其核心职责是为 Web 核心提供一个稳定、高效且功能完备的运行时。</p>
<ul>
<li><strong>应用生命周期管理 (Application Lifecycle):</strong> 负责应用的初始化、主窗口（WebView2）创建与管理，以及应用的正常退出。</li>
<li><strong>硬件抽象层 (Hardware Abstraction Layer - HAL):</strong> 集成并管理打印机、扫描枪等外围设备的 SDK 或驱动，提供统一的硬件访问接口，屏蔽底层硬件的复杂性与实现差异。</li>
<li><strong>系统能力抽象 (System-Level Abstraction):</strong> 封装底层操作系统 API，以标准化的接口形式向 Web 层暴露系统级能力，如文件 I/O、网络状态感知及自动更新服务。</li>
<li><strong>安全通信桥 (Secure Communication Bridge):</strong> 实现一个健壮的<code>JSBridge</code>，确保原生与 Web 之间进行安全、高效、异步的双向数据通信。</li>
</ul>
</li>
<li>
<p><strong>Web 核心 (Web Core - Vue.js 3):</strong> 作为应用的<strong>用户界面与业务逻辑层 (UI &amp; Business Logic Layer)</strong>，全面负责应用的呈现与交互逻辑。</p>
<ul>
<li><strong>声明式 UI 渲染 (Declarative UI Rendering):</strong> 利用 Vue 的响应式系统，高效地将应用状态（State）映射并渲染为用户界面。</li>
<li><strong>业务逻辑编排 (Business Logic Orchestration):</strong> 实现所有业务流程，如商品管理、订单处理、会员服务等，并对原生能力及后端服务进行调用与整合。</li>
<li><strong>集中式状态管理 (Centralized State Management):</strong> 通过 Pinia 对整个应用的共享状态进行统一管理，确保数据流的可预测性与一致性。</li>
<li><strong>API 服务代理 (API Service Proxy):</strong> 封装对后端服务的 HTTP 请求，作为前端与云端服务之间的数据交互代理。</li>
</ul>
</li>
</ul>
</li>
</ul>
<p><strong>架构图:</strong></p>
<ul>
<li>逻辑上 ，Web 层和原生层是两个协作的 并列 模块。</li>
<li>物理上 ，原生层是 宿主 (Host) ，Web 层是运行于其中的 内容 (Content) 。</li>
</ul>
<div class="mermaid">graph TD
    subgraph "POS终端(Windows/Android)"
        subgraph "原生层 (Native Layer - C#/.NET)"
            A[应用生命周期] --&gt; B[主窗口];
            B -- 宿主 --&gt; C{WebView2};
            F[硬件抽象层] -- SDK/驱动 --&gt; G[硬件外设];
            E[系统能力抽象] -- OS API --&gt; D[操作系统];
            H[JSBridge] -- 安全通信 --&gt; C;
            F -- 暴露接口 --&gt; H;
            E -- 暴露接口 --&gt; H;
        end

        subgraph "Web层 (Web Layer - Vue 3)"
            subgraph "UI/视图层 (View Layer)"
              I[页面/组件] -- 渲染 --&gt; C;
            end
            subgraph "逻辑/状态层 (Logic &amp; State Layer)"
              J[Vue Router];
              K[Pinia Store];
              L[API服务];
            end
            I -- 路由 --&gt; J;
            I -- "读/写" --&gt; K;
            I -- 调用 --&gt; L;
            L -- 更新 --&gt; K;
            I -- "通过Bridge调用 jsonrpc2.0" --&gt; H;
        end
    end

    L -- "HTTP/REST" --&gt; M[后端中台服务];

    style C fill:#f9f,stroke:#333,stroke-width:2px
    style H fill:#ccf,stroke:#333,stroke-width:2px
</div><h2 id="2-功能划分原生-vs-web">2. 功能划分：原生 vs. Web </h2>
<h3 id="21-原生-c-实现的功能">2.1. 原生 (C#) 实现的功能 </h3>
<table>
<thead>
<tr>
<th style="text-align:left">功能模块</th>
<th style="text-align:left">技术实现</th>
<th style="text-align:left">理由</th>
</tr>
</thead>
<tbody>
<tr>
<td style="text-align:left">应用窗口/生命周期</td>
<td style="text-align:left">C# WPF, Microsoft.Web.WebView2 控件</td>
<td style="text-align:left">提供无边框窗口、最小化/最大化控制、应用启动退出管理。</td>
</tr>
<tr>
<td style="text-align:left">外设驱动与交互</td>
<td style="text-align:left">System.IO.Ports, P/Invoke, 设备厂商 SDK</td>
<td style="text-align:left">实现高性能、高稳定性的硬件通信，如打印机、扫描枪、钱箱。</td>
</tr>
<tr>
<td style="text-align:left">文件系统访问</td>
<td style="text-align:left"><a href="http://System.IO">System.IO</a> 命名空间</td>
<td style="text-align:left">安全地读写本地日志、用户配置、缓存等。</td>
</tr>
<tr>
<td style="text-align:left">自动更新服务</td>
<td style="text-align:left">自研更新器或 Squirrel.Windows 等框架</td>
<td style="text-align:left">实现后台下载、版本比对、灰度发布和静默/提示安装。</td>
</tr>
<tr>
<td style="text-align:left">网络状态监控</td>
<td style="text-align:left">.NET 网络状态 API</td>
<td style="text-align:left">实时感知网络变化，通知 Web 层以实现离线友好功能。</td>
</tr>
<tr>
<td style="text-align:left">通信桥 (JSBridge)</td>
<td style="text-align:left">WebView2 WebMessageReceived / PostWebMessageAsJson</td>
<td style="text-align:left">搭建原生与 Web 通信的可靠桥梁。使用 JSON-RPC 2.0 协议通信, 确保一致性, 扩展性。</td>
</tr>
<tr>
<td style="text-align:left">应用配置管理</td>
<td style="text-align:left">C# 读取外部 <code>appsettings.json</code> 文件</td>
<td style="text-align:left">实现配置（如 API 地址、门店 ID）与代码分离，便于部署。</td>
</tr>
<tr>
<td style="text-align:left">进程健康监控</td>
<td style="text-align:left">订阅 <code>CoreWebView2.ProcessFailed</code> 事件</td>
<td style="text-align:left">实时、高效地捕获 WebView2 渲染进程崩溃事件，实现应用自动恢复或提示，提升健壮性。</td>
</tr>
<tr>
<td style="text-align:left">WebView2 Runtime 部署</td>
<td style="text-align:left">检测并引导安装 WebView2 Runtime</td>
<td style="text-align:left">确保应用在没有预装 WebView2 的低版本 Windows 系统上也能正常启动和运行。</td>
</tr>
</tbody>
</table>
<h4 id="211-外设输入的捕获与处理">2.1.1. 外设输入的捕获与处理 </h4>
<h4 id="扫码枪">扫码枪 </h4>
<p>在 POS 系统中，扫码枪输入本质上是模拟高速的键盘敲击。</p>
<h4 id="延迟">延迟 </h4>
<ol>
<li>
<p><strong>原生 C#监听路径</strong></p>
<ul>
<li><strong>流程</strong>: <code>硬件</code> → <code>操作系统内核</code> → <code>Win32消息队列</code> → <code>C#全局钩子</code></li>
<li><strong>特点</strong>: 此路径在操作系统底层进行捕获，几乎没有延迟（约 1-5 毫秒），可以非常精确地计算按键之间的时间间隔，从而判断出高速的扫码枪输入。</li>
</ul>
</li>
<li>
<p><strong>WebView 前端监听路径</strong></p>
<ul>
<li><strong>流程</strong>: <code>硬件</code> → <code>操作系统内核</code> → <code>Win32消息队列 </code> → <code>WebView宿主</code> → <code>Chromium渲染进程</code> → <code>JS事件循环</code> → <code>JS keydown监听器</code></li>
<li><strong>特点</strong>: 事件传递路径漫长，涉及多次进程间通信（IPC）。如果 Web 应用本身正忙于渲染或计算，事件处理的延迟可能高达数十甚至上百毫秒。这种不确定的延迟使得在前端通过计时器来区分快慢输入的方法变得极不可靠。</li>
</ul>
</li>
</ol>
<h4 id="最佳实践在原生层进行输入拦截">最佳实践：在原生层进行输入拦截 </h4>
<p>基于以上分析，最稳定、最高效的解决方案是在原生（C#）层面进行全局输入拦截。</p>
<ul>
<li><strong>实现方式</strong>:
<ol>
<li>通过 C#的全局键盘钩子，监听所有的键盘事件。</li>
<li>根据极短的按键时间间隔，精确识别出扫码枪的输入流。</li>
<li>当识别到一个快速的事件序列（判定为扫码枪输入）时，将组合好的完整条码信息，通过 <code>JSBridge</code> 一次性、原子性地发送给前端 Vue 应用进行后续的业务处理。</li>
</ol>
</li>
<li><strong>核心原则</strong>：
<ol>
<li>C#：只负责监听和判断，并发送完整的条码结果。</li>
<li>JS：相信 C#发来的结果是唯一信源，并用它来驱动业务，然后清理现场。</li>
</ol>
</li>
<li><strong>优势</strong>:
<ul>
<li><strong>可靠性高</strong>: 从根源上隔离了两种输入类型，避免了前端复杂的、不可靠的状态判断。</li>
<li><strong>用户体验好</strong>: 对用户完全透明，无论当前焦点在哪里，扫码操作都能被正确捕获和处理。</li>
<li><strong>性能优越</strong>: 避免了在 WebView 中引入额外的、高频的监听器和复杂的逻辑，保证了前端应用的流畅性。</li>
</ul>
</li>
</ul>
<h4 id="212-进程崩溃与恢复使用-processfailed-事件">2.1.2. 进程崩溃与恢复：使用 ProcessFailed 事件 </h4>
<p>为了确保 POS 系统的最高稳定性，必须能够处理 Web 内容进程（即 WebView2 的渲染进程）可能发生的崩溃或无响应。相比于传统的“心跳包”检测机制，WebView2 自身提供了一个更优的解决方案。</p>
<ul>
<li>
<p><strong>核心机制</strong>: <code>CoreWebView2</code> 对象暴露了 <code>ProcessFailed</code> 事件。当 WebView2 的渲染进程或任何关键子进程因任何原因（如内存溢出、渲染引擎错误）意外终止时，原生应用会立即收到此事件通知。</p>
</li>
<li>
<p><strong>优势</strong>:</p>
<ul>
<li><strong>及时性</strong>: 事件触发是即时的，远快于需要等待超时才能判断的心跳机制。</li>
<li><strong>准确性</strong>: 由浏览器内核直接报告进程状态，避免了网络波动等因素对心跳包造成的误判。</li>
<li><strong>高效性</strong>: 无需在 Web 和原生之间建立额外的、消耗资源的定时通信，降低了系统开销。</li>
</ul>
</li>
<li>
<p><strong>实现策略</strong>:</p>
<ol>
<li>在 C# 原生应用初始化 WebView2 控件后，立即订阅 <code>ProcessFailed</code> 事件。</li>
<li>在事件处理程序中，可以根据业务需求执行恢复逻辑，例如：
<ul>
<li>记录详细的崩溃日志。</li>
<li>向用户显示一个友好的错误提示，并提供“重新加载”或“重启应用”的选项。</li>
<li>尝试通过调用 <code>CoreWebView2.Reload()</code> 方法来恢复页面。</li>
<li>如果多次重载失败，则引导用户重启整个应用程序。</li>
</ul>
</li>
</ol>
</li>
</ul>
<p>通过这种方式，我们可以构建一个更加健壮的混合应用，能够从容应对前端内容的意外崩溃，最大程度地保障业务的连续性。</p>
<h4 id="213-webview2-runtime-的安装与部署策略">2.1.3. WebView2 Runtime 的安装与部署策略 </h4>
<p>为了确保应用在用户的设备上（尤其是低版本的 Windows 系统）能够顺利运行，必须妥善处理 WebView2 Runtime 的依赖问题。应用启动时，应首先检测系统是否已安装合适的 WebView2 Runtime。</p>
<ul>
<li>
<p><strong>检测机制</strong>:</p>
<ul>
<li>在 C# 应用启动的早期阶段，调用 <code>Microsoft.Web.WebView2.Core.CoreWebView2Environment.GetAvailableBrowserVersionString()</code> 方法。</li>
<li>如果该方法抛出 <code>WebView2RuntimeNotFoundException</code> 异常，则表明系统缺少 WebView2 Runtime。</li>
</ul>
</li>
<li>
<p><strong>部署策略</strong>:<br>
根据应用的分发方式和用户群体，可以选择以下几种策略之一：</p>
<ol>
<li>
<p><strong>引导用户在线安装 (Evergreen Bootstrapper)</strong>:</p>
<ul>
<li><strong>方式</strong>: 在应用安装包中捆绑一个非常小的引导程序（Bootstrapper）。当检测到 Runtime 缺失时，启动该程序，它会自动从微软服务器下载并安装与用户系统架构匹配的最新版 WebView2 Runtime。</li>
<li><strong>优点</strong>: 安装包体积小，用户始终能获得最新的、包含安全更新的 Runtime。</li>
<li><strong>缺点</strong>: 需要用户设备在安装时能够访问互联网。</li>
<li><strong>适用场景</strong>: 大多数通过网络分发的标准应用。</li>
</ul>
</li>
<li>
<p><strong>捆绑离线安装包 (Evergreen Standalone Installer)</strong>:</p>
<ul>
<li><strong>方式</strong>: 在应用安装包中完整地包含一个特定版本的独立安装程序。当检测到 Runtime 缺失时，静默或提示用户运行此安装程序。</li>
<li><strong>优点</strong>: 无需联网即可完成安装，适合内网或离线环境。</li>
<li><strong>缺点</strong>: 增大了主安装包的体积（约 100MB+）。</li>
<li><strong>适用场景</strong>: 对离线环境有强需求的企业或政府客户。</li>
</ul>
</li>
<li>
<p><strong>固定版本分发 (Fixed Version)</strong>:</p>
<ul>
<li><strong>方式</strong>: 将特定版本的 WebView2 Runtime 的所有文件直接打包到应用目录中。应用只使用这个特定版本的 Runtime。</li>
<li><strong>优点</strong>: 绿色部署，不依赖系统环境，确保了在所有设备上行为完全一致，便于测试和问题复现。</li>
<li><strong>缺点</strong>: 极大地增加了应用包的体积（约 250-300MB），且需要应用自行负责 Runtime 的安全更新。</li>
<li><strong>适用场景</strong>: 对环境一致性有极高要求的特殊场景，如金融、医疗等行业的专用设备。</li>
</ul>
</li>
</ol>
</li>
<li>
<p><strong>用户体验优化</strong>:</p>
<ul>
<li>当检测到需要安装 Runtime 时，应向用户显示清晰、友好的提示界面，说明“正在为您准备必要的运行环境，请稍候...”，而不是直接弹出一个技术性的错误对话框。</li>
<li>如果可能，显示安装进度，以缓解用户的等待焦虑。</li>
<li>安装完成后，自动重新启动应用或 WebView2 控件，无缝衔接进入主程序。</li>
</ul>
</li>
</ul>
<p><strong>本项目推荐策略</strong>：优先采用 <strong>引导用户在线安装 (Evergreen Bootstrapper)</strong> 的方式，因为它在大多数情况下提供了最佳的平衡点：安装包小、用户能享受自动更新带来的安全性和新功能。同时，可以提供一个包含离线安装包的备用下载链接，以满足特殊网络环境下的用户需求。</p>
<h3 id="22-web-vue-3-实现的功能">2.2. Web (Vue 3) 实现的功能 </h3>
<table>
<thead>
<tr>
<th style="text-align:left">功能模块</th>
<th style="text-align:left">技术实现</th>
<th style="text-align:left">理由</th>
</tr>
</thead>
<tbody>
<tr>
<td style="text-align:left">所有 UI 界面</td>
<td style="text-align:left">Vue 3, TDesign, TypeScript</td>
<td style="text-align:left">开发进销存、会员、收银、报表等所有业务界面。</td>
</tr>
<tr>
<td style="text-align:left">业务逻辑</td>
<td style="text-align:left">TypeScript</td>
<td style="text-align:left">处理商品计算、会员积分、促销规则等核心业务。</td>
</tr>
<tr>
<td style="text-align:left">应用路由</td>
<td style="text-align:left">vue-router</td>
<td style="text-align:left">管理单页面应用（SPA）的视图切换。</td>
</tr>
<tr>
<td style="text-align:left">状态管理</td>
<td style="text-align:left">Pinia + <code>pinia-plugin-persistedstate</code></td>
<td style="text-align:left">全局管理购物车、登录用户、从原生接收的系统配置等共享状态，并利用插件将关键状态（如购物车）持久化至<code>localStorage</code>，防止应用意外关闭导致数据丢失。</td>
</tr>
<tr>
<td style="text-align:left">后端 API 通信</td>
<td style="text-align:left">axios 封装</td>
<td style="text-align:left">与 Java 中台进行数据交互，获取和提交业务数据。</td>
</tr>
<tr>
<td style="text-align:left">响应式布局</td>
<td style="text-align:left">CSS 变量动态缩放</td>
<td style="text-align:left">实现跨分辨率的等比缩放界面。</td>
</tr>
<tr>
<td style="text-align:left">用户行为分析</td>
<td style="text-align:left">百度统计 + vue-router 守卫</td>
<td style="text-align:left">收集用户行为数据以支持产品优化。</td>
</tr>
<tr>
<td style="text-align:left">日志上报</td>
<td style="text-align:left">调用原生日志接口</td>
<td style="text-align:left">将前端的操作和错误日志传递给原生层进行统一记录。</td>
</tr>
</tbody>
</table>
<h2 id="3-技术栈-tech-stack">3. 技术栈 (Tech Stack) </h2>
<table>
<thead>
<tr>
<th style="text-align:left">层次</th>
<th style="text-align:left">技术/库</th>
<th style="text-align:left">说明</th>
</tr>
</thead>
<tbody>
<tr>
<td style="text-align:left">原生 (C#)</td>
<td style="text-align:left">.NET 6/8, WinForms/WPF, WebView2, System.Text.Json</td>
<td style="text-align:left"><a href="http://xn--mnq35hl50b2mc.NET">现代化的.NET</a> 平台，高性能的 WebView2（基于 Chromium）。</td>
</tr>
<tr>
<td style="text-align:left">Web 前端</td>
<td style="text-align:left">Vue 3 (Composition API), Vite, TypeScript, TDesign, Pinia, vue-router</td>
<td style="text-align:left">高效的开发体验，强大的类型系统，高质量的 B 端组件库。</td>
</tr>
<tr>
<td style="text-align:left">核心通信</td>
<td style="text-align:left">JSON-RPC 2.0</td>
<td style="text-align:left">轻量、无状态、规范清晰的远程过程调用协议。</td>
</tr>
<tr>
<td style="text-align:left">构建/打包</td>
<td style="text-align:left"><code>dotnet publish</code> (C#), <code>npm run build</code> (Vue)</td>
<td style="text-align:left">分别打包原生应用和 Web 静态资源。</td>
</tr>
<tr>
<td style="text-align:left">代码规范</td>
<td style="text-align:left">ESLint, Prettier, .NET Code Style</td>
<td style="text-align:left">统一团队代码风格，提升代码质量和可维护性。</td>
</tr>
</tbody>
</table>
<h2 id="4-数据流向设计-data-flow">4. 数据流向设计 (Data Flow) </h2>
<h3 id="41-场景一用户点击打印小票">4.1. 场景一：用户点击“打印小票” </h3>
<div class="mermaid">sequenceDiagram
    participant Web as Web (Vue)
    participant Bridge as JSBridge
    participant Native as 原生 (C#)
    participant Printer as 打印机

    Web-&gt;&gt;Bridge: 1. 调用打印接口 (jsonrpc request, id: uuid)
    Bridge-&gt;&gt;Native: 2. 转发请求
    Native-&gt;&gt;Printer: 3. 调用打印机SDK/驱动
    Printer--&gt;&gt;Native: 4. 返回打印状态 (成功/失败)
    Native--&gt;&gt;Bridge: 5. 响应Web (jsonrpc response, id: uuid)
    Bridge--&gt;&gt;Web: 6. 接收并处理打印结果 (Promise resolve/reject)
</div><h3 id="42-场景二应用启动加载商品数据">4.2. 场景二：应用启动加载商品数据 </h3>
<div class="mermaid">sequenceDiagram
    participant Native as 原生 (C#)
    participant Web as Web (Vue)
    participant Pinia as 状态管理
    participant API as 后端Java中台

    Native-&gt;&gt;Web: 1. 启动并加载WebView
    Web-&gt;&gt;API: 2. 通过Axios请求商品列表
    API--&gt;&gt;Web: 3. 返回商品数据
    Web-&gt;&gt;Pinia: 4. 将数据存入Store
    Pinia--&gt;&gt;Web: 5. 更新UI组件，渲染商品列表
</div><h3 id="43-场景三扫描枪扫描商品条码">4.3. 场景三：扫描枪扫描商品条码 </h3>
<div class="mermaid">sequenceDiagram
    participant Scanner as 扫描枪
    participant Native as 原生 (C#)
    participant Bridge as JSBridge
    participant Web as Web (Vue)

    Scanner-&gt;&gt;Native: 1. 模拟键盘输入条码+回车
    Native-&gt;&gt;Bridge: 2. 捕获输入，判断为扫码事件
    Bridge-&gt;&gt;Web: 3. 主动推送扫码事件 (jsonrpc notification)
    Web-&gt;&gt;Web: 4. 接收事件，在收银台页面添加对应商品
</div><h2 id="5-通信规范-communication-protocol">5. 通信规范 (Communication Protocol) </h2>
<p>Web 端与原生应用（Windows C# / Android）的通信是混合应用的核心。为确保通信的 <strong>健壮性、可维护性、类型安全和易用性</strong>，我们设计了以下分层通信规范。</p>
<h3 id="51-核心设计原则">5.1. 核心设计原则 </h3>
<ol>
<li><strong>类型安全 (Type-Safe)</strong>: 利用 TypeScript 在开发阶段捕获所有接口调用的类型错误，杜绝运行时因参数或返回值类型不匹配导致的问题。</li>
<li><strong>契约驱动 (Contract-Driven)</strong>: 所有 Web-Native 交互都必须在统一的“API 契约”中明确定义。此契约是双方通信的唯一真实来源 (Single Source of Truth)。</li>
<li><strong>关注点分离 (Separation of Concerns)</strong>: 将底层的 JSON-RPC 消息处理逻辑与上层的业务 API 调用完全分离。业务开发者只需关心调用哪个方法，而无需处理复杂的消息收发、ID 匹配和超时逻辑。</li>
<li><strong>跨平台兼容 (Cross-Platform)</strong>: 设计一套统一的 JavaScript 接口，使其在 Windows (WebView2) 和 Android (WebView) 平台上表现一致。</li>
</ol>
<h3 id="52-协议与核心数据结构">5.2. 协议与核心数据结构 </h3>
<h4 id="521-底层协议-json-rpc-20">5.2.1. 底层协议: JSON-RPC 2.0 </h4>
<p>我们选用轻量、成熟的 <strong>JSON-RPC 2.0</strong> 作为底层通信协议。它清晰地定义了三种通信模式：</p>
<ul>
<li><strong>请求 (Request)</strong>: Web 端调用原生方法并期望获得响应。包含 <code>id</code>。</li>
<li><strong>响应 (Response)</strong>: 原生端对请求的回应，包含成功 (<code>result</code>) 或失败 (<code>error</code>) 的结果，并携带与请求相同的 <code>id</code>。</li>
<li><strong>通知 (Notification)</strong>: 单向通信，一方通知另一方某事发生，不期望响应。不包含 <code>id</code>。</li>
</ul>
<h4 id="522-标准化业务响应-apiresponset">5.2.2. 标准化业务响应: <code>ApiResponse&lt;T&gt;</code> </h4>
<p>为了让业务逻辑处理更规范，我们规定所有“请求-响应”模式的调用（<code>invoke</code>）都必须返回一个标准化的业务层响应对象，而不是直接返回原生数据。</p>
<p><strong>实现思路</strong>: 这个设计将“通信层成功”与“业务层成功”解耦。例如，一次打印请求，RPC 通信本身可能成功（网络通畅，方法找到），但打印机可能缺纸（业务失败）。<code>ApiResponse</code> 结构可以清晰地表达这种区别。</p>
<pre data-role="codeBlock" data-info="typescript" class="language-typescript typescript"><code><span class="token comment">// file: src/bridge/types.ts</span>

<span class="token doc-comment comment">/**
 * 通用的成功响应结构。
 * <span class="token keyword keyword-@template">@template</span> T - 成功时返回的业务数据类型。
 */</span>
<span class="token keyword keyword-export">export</span> <span class="token keyword keyword-interface">interface</span> <span class="token class-name">SuccessResponse<span class="token operator">&lt;</span><span class="token constant">T</span><span class="token operator">&gt;</span></span> <span class="token punctuation">{</span>
  <span class="token doc-comment comment">/** 操作是否成功，固定为 true */</span>
  success<span class="token operator">:</span> <span class="token boolean">true</span><span class="token punctuation">;</span>
  <span class="token doc-comment comment">/** 业务数据负载 */</span>
  data<span class="token operator">:</span> <span class="token constant">T</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span>

<span class="token doc-comment comment">/**
 * 通用的失败响应结构。
 */</span>
<span class="token keyword keyword-export">export</span> <span class="token keyword keyword-interface">interface</span> <span class="token class-name">ErrorResponse</span> <span class="token punctuation">{</span>
  <span class="token doc-comment comment">/** 操作是否成功，固定为 false */</span>
  success<span class="token operator">:</span> <span class="token boolean">false</span><span class="token punctuation">;</span>
  <span class="token doc-comment comment">/**
   * 业务错误码，用于程序化识别错误类型。
   * 规范建议:
   * - 1000-1999: 通用错误 (如参数无效)
   * - 2000-2999: 打印机相关错误
   * - 3000-3999: 数据库相关错误
   */</span>
  code<span class="token operator">:</span> <span class="token builtin">number</span><span class="token punctuation">;</span>
  <span class="token doc-comment comment">/** 人类可读的错误信息，用于日志记录或向用户展示。 */</span>
  message<span class="token operator">:</span> <span class="token builtin">string</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span>

<span class="token doc-comment comment">/**
 * 标准化的API响应类型。
 * 所有 invoke 方法的 Promise 都应解析为此类型。
 * <span class="token keyword keyword-@template">@template</span> T - 成功时返回的业务数据类型。
 */</span>
<span class="token keyword keyword-export">export</span> <span class="token keyword keyword-type">type</span> <span class="token class-name">ApiResponse<span class="token operator">&lt;</span><span class="token constant">T</span><span class="token operator">&gt;</span></span> <span class="token operator">=</span> SuccessResponse<span class="token operator">&lt;</span><span class="token constant">T</span><span class="token operator">&gt;</span> <span class="token operator">|</span> ErrorResponse<span class="token punctuation">;</span>
</code></pre><h3 id="53-实现方案类型安全的-bridge">5.3. 实现方案：类型安全的 Bridge </h3>
<p>我们将通过三层抽象来实现一个优雅、类型安全的通信方案：</p>
<ol>
<li><strong>类型定义层</strong>: 定义所有通信契约和数据结构。</li>
<li><strong>通信服务层</strong>: 封装底层的消息收发、请求匹配、超时和错误处理。</li>
<li><strong>API 接口层</strong>: 提供给业务代码使用的、完全类型安全的 <code>nativeApi</code> 对象。</li>
</ol>
<h4 id="531-步骤一-定义-api-契约-srcbridgetypests">5.3.1. 步骤一: 定义 API 契约 (<code>src/bridge/types.ts</code>) </h4>
<p>这是整个方案的基石。我们在此文件中定义所有与原生交互的接口、参数和返回值的类型。</p>
<p><strong>实现思路</strong>: 通过一个 <code>ApiContract</code> 接口，我们将所有通信点集中管理。这使得接口变更、查找和维护变得极其简单。TypeScript 将利用这个接口为我们提供强大的自动补全和类型检查。</p>
<pre data-role="codeBlock" data-info="typescript" class="language-typescript typescript"><code><span class="token comment">// file: src/bridge/types.ts</span>

<span class="token comment">// --- 1. 底层 JSON-RPC 结构 ---</span>
<span class="token keyword keyword-export">export</span> <span class="token keyword keyword-interface">interface</span> <span class="token class-name">JsonRpcRequest<span class="token operator">&lt;</span><span class="token constant">T</span> <span class="token operator">=</span> <span class="token builtin">any</span><span class="token operator">&gt;</span></span> <span class="token punctuation">{</span> jsonrpc<span class="token operator">:</span> <span class="token string">"2.0"</span><span class="token punctuation">;</span> method<span class="token operator">:</span> <span class="token builtin">string</span><span class="token punctuation">;</span> params<span class="token operator">?</span><span class="token operator">:</span> <span class="token constant">T</span><span class="token punctuation">;</span> id<span class="token operator">:</span> <span class="token builtin">string</span> <span class="token operator">|</span> <span class="token builtin">number</span><span class="token punctuation">;</span> <span class="token punctuation">}</span>
<span class="token keyword keyword-export">export</span> <span class="token keyword keyword-interface">interface</span> <span class="token class-name">JsonRpcNotification<span class="token operator">&lt;</span><span class="token constant">T</span> <span class="token operator">=</span> <span class="token builtin">any</span><span class="token operator">&gt;</span></span> <span class="token punctuation">{</span> jsonrpc<span class="token operator">:</span> <span class="token string">"2.0"</span><span class="token punctuation">;</span> method<span class="token operator">:</span> <span class="token builtin">string</span><span class="token punctuation">;</span> params<span class="token operator">?</span><span class="token operator">:</span> <span class="token constant">T</span><span class="token punctuation">;</span> <span class="token punctuation">}</span>
<span class="token keyword keyword-export">export</span> <span class="token keyword keyword-interface">interface</span> <span class="token class-name">JsonRpcSuccessResponse<span class="token operator">&lt;</span><span class="token constant">T</span> <span class="token operator">=</span> <span class="token builtin">any</span><span class="token operator">&gt;</span></span> <span class="token punctuation">{</span> jsonrpc<span class="token operator">:</span> <span class="token string">"2.0"</span><span class="token punctuation">;</span> result<span class="token operator">:</span> <span class="token constant">T</span><span class="token punctuation">;</span> id<span class="token operator">:</span> <span class="token builtin">string</span> <span class="token operator">|</span> <span class="token builtin">number</span><span class="token punctuation">;</span> <span class="token punctuation">}</span>
<span class="token keyword keyword-export">export</span> <span class="token keyword keyword-interface">interface</span> <span class="token class-name">JsonRpcErrorResponse</span> <span class="token punctuation">{</span> jsonrpc<span class="token operator">:</span> <span class="token string">"2.0"</span><span class="token punctuation">;</span> error<span class="token operator">:</span> <span class="token punctuation">{</span> code<span class="token operator">:</span> <span class="token builtin">number</span><span class="token punctuation">;</span> message<span class="token operator">:</span> <span class="token builtin">string</span><span class="token punctuation">;</span> data<span class="token operator">?</span><span class="token operator">:</span> <span class="token builtin">any</span><span class="token punctuation">;</span> <span class="token punctuation">}</span><span class="token punctuation">;</span> id<span class="token operator">:</span> <span class="token builtin">string</span> <span class="token operator">|</span> <span class="token builtin">number</span> <span class="token operator">|</span> <span class="token keyword keyword-null">null</span><span class="token punctuation">;</span> <span class="token punctuation">}</span>
<span class="token keyword keyword-export">export</span> <span class="token keyword keyword-type">type</span> <span class="token class-name">JsonRpcResponse<span class="token operator">&lt;</span><span class="token constant">T</span> <span class="token operator">=</span> <span class="token builtin">any</span><span class="token operator">&gt;</span></span> <span class="token operator">=</span> JsonRpcSuccessResponse<span class="token operator">&lt;</span><span class="token constant">T</span><span class="token operator">&gt;</span> <span class="token operator">|</span> JsonRpcErrorResponse<span class="token punctuation">;</span>

<span class="token comment">// --- 2. 标准化业务响应 (已在上方定义) ---</span>
<span class="token comment">// export type { ApiResponse, SuccessResponse, ErrorResponse } from './types'; // This line is conceptually correct but would be in another file.</span>

<span class="token comment">// --- 3. 具体业务的参数与事件负载类型 ---</span>
<span class="token comment">// [调用] 打印小票</span>
<span class="token keyword keyword-export">export</span> <span class="token keyword keyword-interface">interface</span> <span class="token class-name">PrintParams</span> <span class="token punctuation">{</span>
  content<span class="token operator">:</span> object<span class="token punctuation">;</span> <span class="token comment">// 票据内容的结构化数据</span>
  copies<span class="token operator">?</span><span class="token operator">:</span> <span class="token builtin">number</span><span class="token punctuation">;</span> <span class="token comment">// 打印份数</span>
<span class="token punctuation">}</span>

<span class="token comment">// [通知] 记录日志</span>
<span class="token keyword keyword-export">export</span> <span class="token keyword keyword-interface">interface</span> <span class="token class-name">LogParams</span> <span class="token punctuation">{</span>
  level<span class="token operator">:</span> <span class="token string">"info"</span> <span class="token operator">|</span> <span class="token string">"warn"</span> <span class="token operator">|</span> <span class="token string">"error"</span><span class="token punctuation">;</span>
  message<span class="token operator">:</span> <span class="token builtin">string</span><span class="token punctuation">;</span>
  context<span class="token operator">?</span><span class="token operator">:</span> object<span class="token punctuation">;</span> <span class="token comment">// 附加的上下文信息</span>
<span class="token punctuation">}</span>

<span class="token comment">// [事件] 网络状态变更</span>
<span class="token keyword keyword-export">export</span> <span class="token keyword keyword-interface">interface</span> <span class="token class-name">NetworkStatusEvent</span> <span class="token punctuation">{</span>
  online<span class="token operator">:</span> <span class="token builtin">boolean</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span>

<span class="token comment">// [事件] 扫码枪数据</span>
<span class="token keyword keyword-export">export</span> <span class="token keyword keyword-interface">interface</span> <span class="token class-name">ScannerDataEvent</span> <span class="token punctuation">{</span>
  code<span class="token operator">:</span> <span class="token builtin">string</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span>

<span class="token comment">// --- 4. API 契约 (Single Source of Truth) ---</span>
<span class="token doc-comment comment">/**
 * 定义所有 Web 与 Native 之间的通信接口。
 * 这是实现端到端类型安全的核心。
 */</span>
<span class="token keyword keyword-export">export</span> <span class="token keyword keyword-interface">interface</span> <span class="token class-name">ApiContract</span> <span class="token punctuation">{</span>
  <span class="token doc-comment comment">/**
   * 请求/响应模式 (Web 调用 Native，并等待返回结果)
   * `key`: 方法名 (建议使用 `domain.action` 格式)
   * `value`: <span class="token punctuation">{</span> params: 参数类型, result: 返回值类型 (必须是 ApiResponse&lt;T&gt;) <span class="token punctuation">}</span>
   */</span>
  requests<span class="token operator">:</span> <span class="token punctuation">{</span>
    <span class="token string-property property">'printer.print'</span><span class="token operator">:</span> <span class="token punctuation">{</span>
      params<span class="token operator">:</span> PrintParams<span class="token punctuation">;</span>
      result<span class="token operator">:</span> ApiResponse<span class="token operator">&lt;</span><span class="token punctuation">{</span> jobId<span class="token operator">:</span> <span class="token builtin">string</span> <span class="token punctuation">}</span><span class="token operator">&gt;</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span><span class="token punctuation">;</span>
    <span class="token string-property property">'database.query'</span><span class="token operator">:</span> <span class="token punctuation">{</span>
      params<span class="token operator">:</span> <span class="token punctuation">{</span> sql<span class="token operator">:</span> <span class="token builtin">string</span><span class="token punctuation">;</span> args<span class="token operator">:</span> <span class="token builtin">any</span><span class="token punctuation">[</span><span class="token punctuation">]</span> <span class="token punctuation">}</span><span class="token punctuation">;</span>
      result<span class="token operator">:</span> ApiResponse<span class="token operator">&lt;</span><span class="token punctuation">{</span> rows<span class="token operator">:</span> <span class="token builtin">any</span><span class="token punctuation">[</span><span class="token punctuation">]</span> <span class="token punctuation">}</span><span class="token operator">&gt;</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span><span class="token punctuation">;</span>
  <span class="token punctuation">}</span><span class="token punctuation">;</span>

  <span class="token doc-comment comment">/**
   * 通知模式 (Web 调用 Native，不关心返回结果)
   * `key`: 方法名
   * `value`: <span class="token punctuation">{</span> params: 参数类型 <span class="token punctuation">}</span>
   */</span>
  notifications<span class="token operator">:</span> <span class="token punctuation">{</span>
    <span class="token string-property property">'logger.log'</span><span class="token operator">:</span> <span class="token punctuation">{</span> params<span class="token operator">:</span> LogParams <span class="token punctuation">}</span><span class="token punctuation">;</span>
    <span class="token string-property property">'ui.showToast'</span><span class="token operator">:</span> <span class="token punctuation">{</span> params<span class="token operator">:</span> <span class="token punctuation">{</span> message<span class="token operator">:</span> <span class="token builtin">string</span><span class="token punctuation">;</span> duration<span class="token operator">:</span> <span class="token string">'short'</span> <span class="token operator">|</span> <span class="token string">'long'</span> <span class="token punctuation">}</span> <span class="token punctuation">}</span><span class="token punctuation">;</span>
  <span class="token punctuation">}</span><span class="token punctuation">;</span>

  <span class="token doc-comment comment">/**
   * 事件模式 (Native 主动推送到 Web)
   * `key`: 事件名
   * `value`: 事件负载的数据类型
   */</span>
  events<span class="token operator">:</span> <span class="token punctuation">{</span>
    <span class="token string-property property">'network.statusChanged'</span><span class="token operator">:</span> NetworkStatusEvent<span class="token punctuation">;</span>
    <span class="token string-property property">'scanner.dataReceived'</span><span class="token operator">:</span> ScannerDataEvent<span class="token punctuation">;</span>
  <span class="token punctuation">}</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span>
</code></pre><h4 id="532-步骤二-封装底层通信服务-srcservicesbridgets">5.3.2. 步骤二: 封装底层通信服务 (<code>src/services/bridge.ts</code>) </h4>
<p>该服务负责处理所有“脏活累活”，为上层提供干净的 <code>invoke</code>, <code>notify</code>, <code>subscribe</code> 方法。</p>
<p><strong>实现思路</strong>: <code>BridgeService</code> 是一个单例，它在初始化时检测当前环境（Windows 或 Android），并设置相应的消息监听器。它内部维护一个 <code>pendingRequests</code> 映射表，用于在收到响应时，能准确地将结果传递给对应的 Promise。同时，它还管理着事件的订阅与发布。</p>
<pre data-role="codeBlock" data-info="typescript" class="language-typescript typescript"><code><span class="token comment">// file: src/services/bridge.ts</span>
<span class="token keyword keyword-import">import</span> <span class="token punctuation">{</span> v4 <span class="token keyword keyword-as">as</span> uuidv4 <span class="token punctuation">}</span> <span class="token keyword keyword-from">from</span> <span class="token string">"uuid"</span><span class="token punctuation">;</span>
<span class="token keyword keyword-import">import</span> <span class="token keyword keyword-type">type</span> <span class="token punctuation">{</span> JsonRpcRequest<span class="token punctuation">,</span> JsonRpcResponse<span class="token punctuation">,</span> JsonRpcNotification <span class="token punctuation">}</span> <span class="token keyword keyword-from">from</span> <span class="token string">"@/bridge/types"</span><span class="token punctuation">;</span>

<span class="token comment">// --- 类型定义 ---</span>
<span class="token keyword keyword-type">type</span> <span class="token class-name">PendingRequest</span> <span class="token operator">=</span> <span class="token punctuation">{</span> <span class="token function-variable function">resolve</span><span class="token operator">:</span> <span class="token punctuation">(</span>value<span class="token operator">:</span> <span class="token builtin">any</span><span class="token punctuation">)</span> <span class="token operator">=&gt;</span> <span class="token keyword keyword-void">void</span><span class="token punctuation">;</span> <span class="token function-variable function">reject</span><span class="token operator">:</span> <span class="token punctuation">(</span>reason<span class="token operator">?</span><span class="token operator">:</span> <span class="token builtin">any</span><span class="token punctuation">)</span> <span class="token operator">=&gt;</span> <span class="token keyword keyword-void">void</span><span class="token punctuation">;</span> timeoutTimer<span class="token operator">:</span> <span class="token builtin">number</span><span class="token punctuation">;</span> <span class="token punctuation">}</span><span class="token punctuation">;</span>
<span class="token keyword keyword-export">export</span> <span class="token keyword keyword-type">type</span> <span class="token class-name">EventListener</span> <span class="token operator">=</span> <span class="token punctuation">(</span>params<span class="token operator">:</span> <span class="token builtin">any</span><span class="token punctuation">)</span> <span class="token operator">=&gt;</span> <span class="token keyword keyword-void">void</span><span class="token punctuation">;</span>

<span class="token comment">// --- 全局 Window 接口扩展 ---</span>
<span class="token keyword keyword-declare">declare</span> global <span class="token punctuation">{</span>
  <span class="token keyword keyword-interface">interface</span> <span class="token class-name">Window</span> <span class="token punctuation">{</span>
    chrome<span class="token operator">?</span><span class="token operator">:</span> <span class="token punctuation">{</span> webview<span class="token operator">?</span><span class="token operator">:</span> <span class="token punctuation">{</span> <span class="token function-variable function">postMessage</span><span class="token operator">:</span> <span class="token punctuation">(</span>msg<span class="token operator">:</span> <span class="token builtin">any</span><span class="token punctuation">)</span> <span class="token operator">=&gt;</span> <span class="token keyword keyword-void">void</span><span class="token punctuation">;</span> <span class="token function-variable function">addEventListener</span><span class="token operator">:</span> <span class="token punctuation">(</span>type<span class="token operator">:</span> <span class="token builtin">string</span><span class="token punctuation">,</span> <span class="token function-variable function">handler</span><span class="token operator">:</span> <span class="token punctuation">(</span>event<span class="token operator">:</span> <span class="token punctuation">{</span> data<span class="token operator">:</span> <span class="token builtin">any</span> <span class="token punctuation">}</span><span class="token punctuation">)</span> <span class="token operator">=&gt;</span> <span class="token keyword keyword-void">void</span><span class="token punctuation">)</span> <span class="token operator">=&gt;</span> <span class="token keyword keyword-void">void</span><span class="token punctuation">;</span> <span class="token punctuation">}</span><span class="token punctuation">;</span> <span class="token punctuation">}</span><span class="token punctuation">;</span>
    AndroidBridge<span class="token operator">?</span><span class="token operator">:</span> <span class="token punctuation">{</span> <span class="token function-variable function">postMessage</span><span class="token operator">:</span> <span class="token punctuation">(</span>msg<span class="token operator">:</span> <span class="token builtin">string</span><span class="token punctuation">)</span> <span class="token operator">=&gt;</span> <span class="token keyword keyword-void">void</span><span class="token punctuation">;</span> <span class="token punctuation">}</span><span class="token punctuation">;</span>
    onNativeMessage<span class="token operator">?</span><span class="token operator">:</span> <span class="token punctuation">(</span>msg<span class="token operator">:</span> <span class="token builtin">string</span><span class="token punctuation">)</span> <span class="token operator">=&gt;</span> <span class="token keyword keyword-void">void</span><span class="token punctuation">;</span>
  <span class="token punctuation">}</span>
<span class="token punctuation">}</span>

<span class="token doc-comment comment">/**
 * 底层通信服务，处理与原生环境的 JSON-RPC 消息交换。
 * 这个类被设计为内部使用，业务代码不应直接与之交互。
 */</span>
<span class="token keyword keyword-class">class</span> <span class="token class-name">BridgeService</span> <span class="token punctuation">{</span>
  <span class="token keyword keyword-private">private</span> pendingRequests <span class="token operator">=</span> <span class="token keyword keyword-new">new</span> <span class="token class-name">Map<span class="token operator">&lt;</span><span class="token builtin">string</span> <span class="token operator">|</span> <span class="token builtin">number</span><span class="token punctuation">,</span> PendingRequest<span class="token operator">&gt;</span></span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
  <span class="token keyword keyword-private">private</span> eventListeners <span class="token operator">=</span> <span class="token keyword keyword-new">new</span> <span class="token class-name">Map<span class="token operator">&lt;</span><span class="token builtin">string</span><span class="token punctuation">,</span> Set<span class="token operator">&lt;</span>EventListener<span class="token operator">&gt;&gt;</span></span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
  <span class="token keyword keyword-private">private</span> <span class="token keyword keyword-readonly">readonly</span> <span class="token constant">DEFAULT_TIMEOUT</span> <span class="token operator">=</span> <span class="token number">15000</span><span class="token punctuation">;</span> <span class="token comment">// 15秒超时</span>
  <span class="token keyword keyword-private">private</span> platform<span class="token operator">:</span> <span class="token string">"windows"</span> <span class="token operator">|</span> <span class="token string">"android"</span> <span class="token operator">|</span> <span class="token string">"unknown"</span> <span class="token operator">=</span> <span class="token string">"unknown"</span><span class="token punctuation">;</span>

  <span class="token function">constructor</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
    <span class="token keyword keyword-this">this</span><span class="token punctuation">.</span><span class="token function">detectPlatform</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-this">this</span><span class="token punctuation">.</span><span class="token function">setupMessageListener</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
  <span class="token punctuation">}</span>

  <span class="token comment">// 1. 环境检测与监听设置</span>
  <span class="token keyword keyword-private">private</span> <span class="token function">detectPlatform</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">:</span> <span class="token keyword keyword-void">void</span> <span class="token punctuation">{</span>
    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>window<span class="token punctuation">.</span>chrome<span class="token operator">?.</span>webview<span class="token punctuation">)</span> <span class="token keyword keyword-this">this</span><span class="token punctuation">.</span>platform <span class="token operator">=</span> <span class="token string">"windows"</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-else">else</span> <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>window<span class="token punctuation">.</span>AndroidBridge<span class="token punctuation">)</span> <span class="token keyword keyword-this">this</span><span class="token punctuation">.</span>platform <span class="token operator">=</span> <span class="token string">"android"</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-else">else</span> <span class="token punctuation">{</span>
        <span class="token keyword keyword-this">this</span><span class="token punctuation">.</span>platform <span class="token operator">=</span> <span class="token string">"unknown"</span><span class="token punctuation">;</span>
        <span class="token keyword keyword-this">this</span><span class="token punctuation">.</span><span class="token function">_logError</span><span class="token punctuation">(</span><span class="token string">"Bridge platform detection failed. No native bridge found."</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>
  <span class="token punctuation">}</span>

  <span class="token keyword keyword-private">private</span> <span class="token function">setupMessageListener</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">:</span> <span class="token keyword keyword-void">void</span> <span class="token punctuation">{</span>
    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span><span class="token keyword keyword-this">this</span><span class="token punctuation">.</span>platform <span class="token operator">===</span> <span class="token string">'windows'</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
      window<span class="token punctuation">.</span>chrome<span class="token operator">!</span><span class="token punctuation">.</span>webview<span class="token operator">!</span><span class="token punctuation">.</span><span class="token function">addEventListener</span><span class="token punctuation">(</span><span class="token string">"message"</span><span class="token punctuation">,</span> <span class="token punctuation">(</span>event<span class="token punctuation">)</span> <span class="token operator">=&gt;</span> <span class="token keyword keyword-this">this</span><span class="token punctuation">.</span><span class="token function">handleNativeMessage</span><span class="token punctuation">(</span>event<span class="token punctuation">.</span>data<span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span> <span class="token keyword keyword-else">else</span> <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span><span class="token keyword keyword-this">this</span><span class="token punctuation">.</span>platform <span class="token operator">===</span> <span class="token string">'android'</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
      window<span class="token punctuation">.</span><span class="token function-variable function">onNativeMessage</span> <span class="token operator">=</span> <span class="token punctuation">(</span>message<span class="token operator">:</span> <span class="token builtin">string</span><span class="token punctuation">)</span> <span class="token operator">=&gt;</span> <span class="token punctuation">{</span>
        <span class="token keyword keyword-try">try</span> <span class="token punctuation">{</span>
          <span class="token keyword keyword-this">this</span><span class="token punctuation">.</span><span class="token function">handleNativeMessage</span><span class="token punctuation">(</span><span class="token constant">JSON</span><span class="token punctuation">.</span><span class="token function">parse</span><span class="token punctuation">(</span>message<span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token punctuation">}</span> <span class="token keyword keyword-catch">catch</span> <span class="token punctuation">(</span>e<span class="token punctuation">)</span> <span class="token punctuation">{</span>
          <span class="token keyword keyword-this">this</span><span class="token punctuation">.</span><span class="token function">_logError</span><span class="token punctuation">(</span><span class="token string">"Failed to parse message from Android"</span><span class="token punctuation">,</span> <span class="token punctuation">{</span> originalMessage<span class="token operator">:</span> message<span class="token punctuation">,</span> error<span class="token operator">:</span> e <span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token punctuation">}</span>
      <span class="token punctuation">}</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>
  <span class="token punctuation">}</span>

  <span class="token comment">// 2. 核心消息处理</span>
  <span class="token keyword keyword-private">private</span> <span class="token function">handleNativeMessage</span><span class="token punctuation">(</span>message<span class="token operator">:</span> JsonRpcResponse <span class="token operator">|</span> JsonRpcNotification<span class="token punctuation">)</span><span class="token operator">:</span> <span class="token keyword keyword-void">void</span> <span class="token punctuation">{</span>
    <span class="token comment">// Case A: 是一个响应 (有 id)</span>
    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span><span class="token string">"id"</span> <span class="token keyword keyword-in">in</span> message <span class="token operator">&amp;&amp;</span> message<span class="token punctuation">.</span>id <span class="token operator">!==</span> <span class="token keyword keyword-null">null</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
      <span class="token keyword keyword-const">const</span> pending <span class="token operator">=</span> <span class="token keyword keyword-this">this</span><span class="token punctuation">.</span>pendingRequests<span class="token punctuation">.</span><span class="token function">get</span><span class="token punctuation">(</span>message<span class="token punctuation">.</span>id<span class="token punctuation">)</span><span class="token punctuation">;</span>
      <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>pending<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token function">clearTimeout</span><span class="token punctuation">(</span>pending<span class="token punctuation">.</span>timeoutTimer<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span><span class="token string">"result"</span> <span class="token keyword keyword-in">in</span> message<span class="token punctuation">)</span> <span class="token punctuation">{</span>
          pending<span class="token punctuation">.</span><span class="token function">resolve</span><span class="token punctuation">(</span>message<span class="token punctuation">.</span>result<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token punctuation">}</span> <span class="token keyword keyword-else">else</span> <span class="token punctuation">{</span>
          <span class="token keyword keyword-this">this</span><span class="token punctuation">.</span><span class="token function">_logError</span><span class="token punctuation">(</span><span class="token string">"Received error response from native"</span><span class="token punctuation">,</span> <span class="token punctuation">{</span> id<span class="token operator">:</span> message<span class="token punctuation">.</span>id<span class="token punctuation">,</span> error<span class="token operator">:</span> message<span class="token punctuation">.</span>error <span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
          pending<span class="token punctuation">.</span><span class="token function">reject</span><span class="token punctuation">(</span>message<span class="token punctuation">.</span>error<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token punctuation">}</span>
        <span class="token keyword keyword-this">this</span><span class="token punctuation">.</span>pendingRequests<span class="token punctuation">.</span><span class="token function">delete</span><span class="token punctuation">(</span>message<span class="token punctuation">.</span>id<span class="token punctuation">)</span><span class="token punctuation">;</span>
      <span class="token punctuation">}</span>
    <span class="token punctuation">}</span> 
    <span class="token comment">// Case B: 是一个事件/通知 (没有 id)</span>
    <span class="token keyword keyword-else">else</span> <span class="token punctuation">{</span>
      <span class="token keyword keyword-this">this</span><span class="token punctuation">.</span><span class="token function">publish</span><span class="token punctuation">(</span>message<span class="token punctuation">.</span>method<span class="token punctuation">,</span> message<span class="token punctuation">.</span>params<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>
  <span class="token punctuation">}</span>

  <span class="token comment">// 3. 核心消息发送</span>
  <span class="token keyword keyword-private">private</span> <span class="token function">postMessage</span><span class="token punctuation">(</span>message<span class="token operator">:</span> JsonRpcRequest <span class="token operator">|</span> JsonRpcNotification<span class="token punctuation">)</span><span class="token operator">:</span> <span class="token keyword keyword-void">void</span> <span class="token punctuation">{</span>
    <span class="token keyword keyword-try">try</span> <span class="token punctuation">{</span>
      <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span><span class="token keyword keyword-this">this</span><span class="token punctuation">.</span>platform <span class="token operator">===</span> <span class="token string">'windows'</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
        window<span class="token punctuation">.</span>chrome<span class="token operator">!</span><span class="token punctuation">.</span>webview<span class="token operator">!</span><span class="token punctuation">.</span><span class="token function">postMessage</span><span class="token punctuation">(</span>message<span class="token punctuation">)</span><span class="token punctuation">;</span>
      <span class="token punctuation">}</span> <span class="token keyword keyword-else">else</span> <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span><span class="token keyword keyword-this">this</span><span class="token punctuation">.</span>platform <span class="token operator">===</span> <span class="token string">'android'</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
        window<span class="token punctuation">.</span>AndroidBridge<span class="token operator">!</span><span class="token punctuation">.</span><span class="token function">postMessage</span><span class="token punctuation">(</span><span class="token constant">JSON</span><span class="token punctuation">.</span><span class="token function">stringify</span><span class="token punctuation">(</span>message<span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
      <span class="token punctuation">}</span> <span class="token keyword keyword-else">else</span> <span class="token punctuation">{</span>
        <span class="token comment">// 这个错误理论上在 detectPlatform 时已经记录，但作为防御性编程保留</span>
        <span class="token keyword keyword-throw">throw</span> <span class="token keyword keyword-new">new</span> <span class="token class-name">Error</span><span class="token punctuation">(</span><span class="token string">"Native bridge is not available."</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
      <span class="token punctuation">}</span>
    <span class="token punctuation">}</span> <span class="token keyword keyword-catch">catch</span> <span class="token punctuation">(</span>error<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token keyword keyword-this">this</span><span class="token punctuation">.</span><span class="token function">_logError</span><span class="token punctuation">(</span><span class="token template-string"><span class="token template-punctuation string">`</span><span class="token string">Failed to post message for method: </span><span class="token interpolation"><span class="token interpolation-punctuation punctuation">${</span>message<span class="token punctuation">.</span>method<span class="token interpolation-punctuation punctuation">}</span></span><span class="token template-punctuation string">`</span></span><span class="token punctuation">,</span> <span class="token punctuation">{</span> message<span class="token punctuation">,</span> error <span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token comment">// 重新抛出错误，让调用者知道发送失败</span>
        <span class="token keyword keyword-throw">throw</span> error<span class="token punctuation">;</span>
    <span class="token punctuation">}</span>
  <span class="token punctuation">}</span>

  <span class="token comment">// 4. 暴露给上层 API 的公共方法</span>
  <span class="token keyword keyword-public">public</span> <span class="token generic-function"><span class="token function">invoke</span><span class="token generic class-name"><span class="token operator">&lt;</span>TResult <span class="token operator">=</span> <span class="token builtin">any</span><span class="token punctuation">,</span> TParams <span class="token operator">=</span> <span class="token builtin">any</span><span class="token operator">&gt;</span></span></span><span class="token punctuation">(</span>method<span class="token operator">:</span> <span class="token builtin">string</span><span class="token punctuation">,</span> params<span class="token operator">:</span> TParams<span class="token punctuation">)</span><span class="token operator">:</span> <span class="token builtin">Promise</span><span class="token operator">&lt;</span>TResult<span class="token operator">&gt;</span> <span class="token punctuation">{</span>
    <span class="token keyword keyword-const">const</span> id <span class="token operator">=</span> <span class="token function">uuidv4</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-const">const</span> request<span class="token operator">:</span> JsonRpcRequest<span class="token operator">&lt;</span>TParams<span class="token operator">&gt;</span> <span class="token operator">=</span> <span class="token punctuation">{</span> jsonrpc<span class="token operator">:</span> <span class="token string">"2.0"</span><span class="token punctuation">,</span> method<span class="token punctuation">,</span> params<span class="token punctuation">,</span> id <span class="token punctuation">}</span><span class="token punctuation">;</span>

    <span class="token keyword keyword-return">return</span> <span class="token keyword keyword-new">new</span> <span class="token class-name"><span class="token builtin">Promise</span><span class="token operator">&lt;</span>TResult<span class="token operator">&gt;</span></span><span class="token punctuation">(</span><span class="token punctuation">(</span>resolve<span class="token punctuation">,</span> reject<span class="token punctuation">)</span> <span class="token operator">=&gt;</span> <span class="token punctuation">{</span>
      <span class="token keyword keyword-const">const</span> timeoutTimer <span class="token operator">=</span> window<span class="token punctuation">.</span><span class="token function">setTimeout</span><span class="token punctuation">(</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token operator">=&gt;</span> <span class="token punctuation">{</span>
        <span class="token keyword keyword-this">this</span><span class="token punctuation">.</span>pendingRequests<span class="token punctuation">.</span><span class="token function">delete</span><span class="token punctuation">(</span>id<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token keyword keyword-const">const</span> error <span class="token operator">=</span> <span class="token punctuation">{</span> code<span class="token operator">:</span> <span class="token operator">-</span><span class="token number">32000</span><span class="token punctuation">,</span> message<span class="token operator">:</span> <span class="token template-string"><span class="token template-punctuation string">`</span><span class="token string">Request timed out after </span><span class="token interpolation"><span class="token interpolation-punctuation punctuation">${</span><span class="token keyword keyword-this">this</span><span class="token punctuation">.</span><span class="token constant">DEFAULT_TIMEOUT</span><span class="token interpolation-punctuation punctuation">}</span></span><span class="token string">ms</span><span class="token template-punctuation string">`</span></span> <span class="token punctuation">}</span><span class="token punctuation">;</span>
        <span class="token keyword keyword-this">this</span><span class="token punctuation">.</span><span class="token function">_logError</span><span class="token punctuation">(</span><span class="token template-string"><span class="token template-punctuation string">`</span><span class="token string">Request timed out for method: </span><span class="token interpolation"><span class="token interpolation-punctuation punctuation">${</span>method<span class="token interpolation-punctuation punctuation">}</span></span><span class="token template-punctuation string">`</span></span><span class="token punctuation">,</span> <span class="token punctuation">{</span> id<span class="token punctuation">,</span> timeout<span class="token operator">:</span> <span class="token keyword keyword-this">this</span><span class="token punctuation">.</span><span class="token constant">DEFAULT_TIMEOUT</span> <span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token function">reject</span><span class="token punctuation">(</span>error<span class="token punctuation">)</span><span class="token punctuation">;</span>
      <span class="token punctuation">}</span><span class="token punctuation">,</span> <span class="token keyword keyword-this">this</span><span class="token punctuation">.</span><span class="token constant">DEFAULT_TIMEOUT</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

      <span class="token keyword keyword-this">this</span><span class="token punctuation">.</span>pendingRequests<span class="token punctuation">.</span><span class="token function">set</span><span class="token punctuation">(</span>id<span class="token punctuation">,</span> <span class="token punctuation">{</span> resolve<span class="token punctuation">,</span> reject<span class="token punctuation">,</span> timeoutTimer <span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

      <span class="token keyword keyword-try">try</span> <span class="token punctuation">{</span> <span class="token keyword keyword-this">this</span><span class="token punctuation">.</span><span class="token function">postMessage</span><span class="token punctuation">(</span>request<span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token punctuation">}</span>
      <span class="token keyword keyword-catch">catch</span> <span class="token punctuation">(</span>e<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token function">clearTimeout</span><span class="token punctuation">(</span>timeoutTimer<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token keyword keyword-this">this</span><span class="token punctuation">.</span>pendingRequests<span class="token punctuation">.</span><span class="token function">delete</span><span class="token punctuation">(</span>id<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token function">reject</span><span class="token punctuation">(</span><span class="token punctuation">{</span> code<span class="token operator">:</span> <span class="token operator">-</span><span class="token number">32001</span><span class="token punctuation">,</span> message<span class="token operator">:</span> <span class="token string">"Failed to send message to native"</span><span class="token punctuation">,</span> data<span class="token operator">:</span> e <span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
      <span class="token punctuation">}</span>
    <span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
  <span class="token punctuation">}</span>

  <span class="token keyword keyword-public">public</span> <span class="token generic-function"><span class="token function">notify</span><span class="token generic class-name"><span class="token operator">&lt;</span>TParams <span class="token operator">=</span> <span class="token builtin">any</span><span class="token operator">&gt;</span></span></span><span class="token punctuation">(</span>method<span class="token operator">:</span> <span class="token builtin">string</span><span class="token punctuation">,</span> params<span class="token operator">:</span> TParams<span class="token punctuation">)</span><span class="token operator">:</span> <span class="token keyword keyword-void">void</span> <span class="token punctuation">{</span>
    <span class="token keyword keyword-const">const</span> notification<span class="token operator">:</span> JsonRpcNotification<span class="token operator">&lt;</span>TParams<span class="token operator">&gt;</span> <span class="token operator">=</span> <span class="token punctuation">{</span> jsonrpc<span class="token operator">:</span> <span class="token string">"2.0"</span><span class="token punctuation">,</span> method<span class="token punctuation">,</span> params <span class="token punctuation">}</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-try">try</span> <span class="token punctuation">{</span>
      <span class="token keyword keyword-this">this</span><span class="token punctuation">.</span><span class="token function">postMessage</span><span class="token punctuation">(</span>notification<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span> <span class="token keyword keyword-catch">catch</span> <span class="token punctuation">(</span>e<span class="token punctuation">)</span> <span class="token punctuation">{</span>
      <span class="token comment">// 虽然 notify 是“即发即忘”，但记录发送失败的错误对于调试仍然至关重要</span>
      <span class="token keyword keyword-this">this</span><span class="token punctuation">.</span><span class="token function">_logError</span><span class="token punctuation">(</span><span class="token template-string"><span class="token template-punctuation string">`</span><span class="token string">Failed to send notification for method: </span><span class="token interpolation"><span class="token interpolation-punctuation punctuation">${</span>method<span class="token interpolation-punctuation punctuation">}</span></span><span class="token template-punctuation string">`</span></span><span class="token punctuation">,</span> <span class="token punctuation">{</span> error<span class="token operator">:</span> e <span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>
  <span class="token punctuation">}</span>

  <span class="token keyword keyword-public">public</span> <span class="token function">subscribe</span><span class="token punctuation">(</span>eventName<span class="token operator">:</span> <span class="token builtin">string</span><span class="token punctuation">,</span> listener<span class="token operator">:</span> EventListener<span class="token punctuation">)</span><span class="token operator">:</span> <span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token operator">=&gt;</span> <span class="token keyword keyword-void">void</span> <span class="token punctuation">{</span>
    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span><span class="token operator">!</span><span class="token keyword keyword-this">this</span><span class="token punctuation">.</span>eventListeners<span class="token punctuation">.</span><span class="token function">has</span><span class="token punctuation">(</span>eventName<span class="token punctuation">)</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
      <span class="token keyword keyword-this">this</span><span class="token punctuation">.</span>eventListeners<span class="token punctuation">.</span><span class="token function">set</span><span class="token punctuation">(</span>eventName<span class="token punctuation">,</span> <span class="token keyword keyword-new">new</span> <span class="token class-name">Set</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>
    <span class="token keyword keyword-this">this</span><span class="token punctuation">.</span>eventListeners<span class="token punctuation">.</span><span class="token function">get</span><span class="token punctuation">(</span>eventName<span class="token punctuation">)</span><span class="token operator">!</span><span class="token punctuation">.</span><span class="token function">add</span><span class="token punctuation">(</span>listener<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-return">return</span> <span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token operator">=&gt;</span> <span class="token punctuation">{</span> <span class="token keyword keyword-this">this</span><span class="token punctuation">.</span>eventListeners<span class="token punctuation">.</span><span class="token function">get</span><span class="token punctuation">(</span>eventName<span class="token punctuation">)</span><span class="token operator">?.</span><span class="token function">delete</span><span class="token punctuation">(</span>listener<span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token punctuation">}</span><span class="token punctuation">;</span>
  <span class="token punctuation">}</span>

  <span class="token keyword keyword-private">private</span> <span class="token function">publish</span><span class="token punctuation">(</span>eventName<span class="token operator">:</span> <span class="token builtin">string</span><span class="token punctuation">,</span> data<span class="token operator">:</span> <span class="token builtin">any</span><span class="token punctuation">)</span><span class="token operator">:</span> <span class="token keyword keyword-void">void</span> <span class="token punctuation">{</span>
    <span class="token keyword keyword-this">this</span><span class="token punctuation">.</span>eventListeners<span class="token punctuation">.</span><span class="token function">get</span><span class="token punctuation">(</span>eventName<span class="token punctuation">)</span><span class="token operator">?.</span><span class="token function">forEach</span><span class="token punctuation">(</span><span class="token punctuation">(</span>listener<span class="token punctuation">)</span> <span class="token operator">=&gt;</span> <span class="token punctuation">{</span>
      <span class="token keyword keyword-try">try</span> <span class="token punctuation">{</span>
        <span class="token function">listener</span><span class="token punctuation">(</span>data<span class="token punctuation">)</span><span class="token punctuation">;</span>
      <span class="token punctuation">}</span> <span class="token keyword keyword-catch">catch</span> <span class="token punctuation">(</span>e<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token keyword keyword-this">this</span><span class="token punctuation">.</span><span class="token function">_logError</span><span class="token punctuation">(</span><span class="token template-string"><span class="token template-punctuation string">`</span><span class="token string">Error in event listener for '</span><span class="token interpolation"><span class="token interpolation-punctuation punctuation">${</span>eventName<span class="token interpolation-punctuation punctuation">}</span></span><span class="token string">'</span><span class="token template-punctuation string">`</span></span><span class="token punctuation">,</span> <span class="token punctuation">{</span> error<span class="token operator">:</span> e <span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
      <span class="token punctuation">}</span>
    <span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
  <span class="token punctuation">}</span>

  <span class="token comment">// 5. 统一错误处理</span>
  <span class="token keyword keyword-private">private</span> <span class="token function">_logError</span><span class="token punctuation">(</span>summary<span class="token operator">:</span> <span class="token builtin">string</span><span class="token punctuation">,</span> context<span class="token operator">:</span> object <span class="token operator">=</span> <span class="token punctuation">{</span><span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token operator">:</span> <span class="token keyword keyword-void">void</span> <span class="token punctuation">{</span>
    <span class="token builtin">console</span><span class="token punctuation">.</span><span class="token function">error</span><span class="token punctuation">(</span><span class="token template-string"><span class="token template-punctuation string">`</span><span class="token string">[BridgeService] </span><span class="token interpolation"><span class="token interpolation-punctuation punctuation">${</span>summary<span class="token interpolation-punctuation punctuation">}</span></span><span class="token template-punctuation string">`</span></span><span class="token punctuation">,</span> context<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token comment">// 在未来，这里可以扩展为上报到远程日志服务，如 Sentry, LogRocket 等</span>
    <span class="token comment">// e.g., Sentry.captureException(new Error(summary), { extra: context });</span>
  <span class="token punctuation">}</span>
<span class="token punctuation">}</span>

<span class="token comment">// 导出 BridgeService 的单例，供 nativeApi 使用</span>
<span class="token keyword keyword-export">export</span> <span class="token keyword keyword-const">const</span> bridge <span class="token operator">=</span> <span class="token keyword keyword-new">new</span> <span class="token class-name">BridgeService</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
</code></pre><h4 id="533-步骤三-创建强类型-api-接口-srcservicesnativeapits">5.3.3. 步骤三: 创建强类型 API 接口 (<code>src/services/nativeApi.ts</code>) </h4>
<p>为了在业务代码中提供极致的类型安全和开发体验，我们创建一个 <code>nativeApi</code> 对象。该对象为 <code>ApiContract</code> 中的每一个接口都创建一个具名的、显式的方法，使得调用就像操作一个普通的 JavaScript 对象一样简单直观。</p>
<p>这种显式定义的方法虽然在新增接口时需要少量重复工作，但其带来的代码清晰度和对所有水平开发者（尤其是对 TypeScript 高级特性不熟悉的开发者）的友好性，使其成为一个稳健且易于维护的选择。</p>
<p><strong>实现 (<code>src/services/nativeApi.ts</code>)</strong>:</p>
<pre data-role="codeBlock" data-info="typescript" class="language-typescript typescript"><code><span class="token comment">// file: src/services/nativeApi.ts</span>
<span class="token keyword keyword-import">import</span> <span class="token punctuation">{</span> bridge <span class="token punctuation">}</span> <span class="token keyword keyword-from">from</span> <span class="token string">'./bridge'</span><span class="token punctuation">;</span>
<span class="token keyword keyword-import">import</span> <span class="token keyword keyword-type">type</span> <span class="token punctuation">{</span> 
  PrintParams<span class="token punctuation">,</span> 
  LogParams<span class="token punctuation">,</span> 
  NetworkStatusEvent<span class="token punctuation">,</span> 
  ScannerDataEvent<span class="token punctuation">,</span> 
  ApiResponse 
<span class="token punctuation">}</span> <span class="token keyword keyword-from">from</span> <span class="token string">'@/bridge/types'</span><span class="token punctuation">;</span>

<span class="token doc-comment comment">/**
 * 完全类型安全的 Native API 对象 (显式版本)。
 * 结构与 ApiContract 保持一致，易于理解。
 */</span>
<span class="token keyword keyword-export">export</span> <span class="token keyword keyword-const">const</span> nativeApi <span class="token operator">=</span> <span class="token punctuation">{</span>
  <span class="token comment">// --- Requests (对应 ApiContract['requests']) ---</span>
  printer<span class="token operator">:</span> <span class="token punctuation">{</span>
    print<span class="token operator">:</span> <span class="token punctuation">(</span>params<span class="token operator">:</span> PrintParams<span class="token punctuation">)</span><span class="token operator">:</span> <span class="token builtin">Promise</span><span class="token operator">&lt;</span>ApiResponse<span class="token operator">&lt;</span><span class="token punctuation">{</span> jobId<span class="token operator">:</span> <span class="token builtin">string</span> <span class="token punctuation">}</span><span class="token operator">&gt;&gt;</span> <span class="token operator">=&gt;</span> <span class="token punctuation">{</span>
      <span class="token keyword keyword-return">return</span> bridge<span class="token punctuation">.</span><span class="token function">invoke</span><span class="token punctuation">(</span><span class="token string">'printer.print'</span><span class="token punctuation">,</span> params<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span><span class="token punctuation">,</span>
  <span class="token punctuation">}</span><span class="token punctuation">,</span>
  database<span class="token operator">:</span> <span class="token punctuation">{</span>
    query<span class="token operator">:</span> <span class="token punctuation">(</span>params<span class="token operator">:</span> <span class="token punctuation">{</span> sql<span class="token operator">:</span> <span class="token builtin">string</span><span class="token punctuation">;</span> args<span class="token operator">:</span> <span class="token builtin">any</span><span class="token punctuation">[</span><span class="token punctuation">]</span> <span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token operator">:</span> <span class="token builtin">Promise</span><span class="token operator">&lt;</span>ApiResponse<span class="token operator">&lt;</span><span class="token punctuation">{</span> rows<span class="token operator">:</span> <span class="token builtin">any</span><span class="token punctuation">[</span><span class="token punctuation">]</span> <span class="token punctuation">}</span><span class="token operator">&gt;&gt;</span> <span class="token operator">=&gt;</span> <span class="token punctuation">{</span>
      <span class="token keyword keyword-return">return</span> bridge<span class="token punctuation">.</span><span class="token function">invoke</span><span class="token punctuation">(</span><span class="token string">'database.query'</span><span class="token punctuation">,</span> params<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span><span class="token punctuation">,</span>
  <span class="token punctuation">}</span><span class="token punctuation">,</span>

  <span class="token comment">// --- Notifications (对应 ApiContract['notifications']) ---</span>
  logger<span class="token operator">:</span> <span class="token punctuation">{</span>
    log<span class="token operator">:</span> <span class="token punctuation">(</span>params<span class="token operator">:</span> LogParams<span class="token punctuation">)</span><span class="token operator">:</span> <span class="token keyword keyword-void">void</span> <span class="token operator">=&gt;</span> <span class="token punctuation">{</span>
      bridge<span class="token punctuation">.</span><span class="token function">notify</span><span class="token punctuation">(</span><span class="token string">'logger.log'</span><span class="token punctuation">,</span> params<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span><span class="token punctuation">,</span>
  <span class="token punctuation">}</span><span class="token punctuation">,</span>
  ui<span class="token operator">:</span> <span class="token punctuation">{</span>
    showToast<span class="token operator">:</span> <span class="token punctuation">(</span>params<span class="token operator">:</span> <span class="token punctuation">{</span> message<span class="token operator">:</span> <span class="token builtin">string</span><span class="token punctuation">;</span> duration<span class="token operator">:</span> <span class="token string">'short'</span> <span class="token operator">|</span> <span class="token string">'long'</span> <span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token operator">:</span> <span class="token keyword keyword-void">void</span> <span class="token operator">=&gt;</span> <span class="token punctuation">{</span>
      bridge<span class="token punctuation">.</span><span class="token function">notify</span><span class="token punctuation">(</span><span class="token string">'ui.showToast'</span><span class="token punctuation">,</span> params<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span><span class="token punctuation">,</span>
  <span class="token punctuation">}</span><span class="token punctuation">,</span>

  <span class="token comment">// --- Events (对应 ApiContract['events']) ---</span>
  events<span class="token operator">:</span> <span class="token punctuation">{</span>
    network<span class="token operator">:</span> <span class="token punctuation">{</span>
      statusChanged<span class="token operator">:</span> <span class="token punctuation">{</span>
        subscribe<span class="token operator">:</span> <span class="token punctuation">(</span><span class="token function-variable function">listener</span><span class="token operator">:</span> <span class="token punctuation">(</span>data<span class="token operator">:</span> NetworkStatusEvent<span class="token punctuation">)</span> <span class="token operator">=&gt;</span> <span class="token keyword keyword-void">void</span><span class="token punctuation">)</span><span class="token operator">:</span> <span class="token punctuation">(</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token operator">=&gt;</span> <span class="token keyword keyword-void">void</span><span class="token punctuation">)</span> <span class="token operator">=&gt;</span> <span class="token punctuation">{</span>
          <span class="token keyword keyword-return">return</span> bridge<span class="token punctuation">.</span><span class="token function">subscribe</span><span class="token punctuation">(</span><span class="token string">'network.statusChanged'</span><span class="token punctuation">,</span> listener<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token punctuation">}</span><span class="token punctuation">,</span>
      <span class="token punctuation">}</span><span class="token punctuation">,</span>
    <span class="token punctuation">}</span><span class="token punctuation">,</span>
    scanner<span class="token operator">:</span> <span class="token punctuation">{</span>
      dataReceived<span class="token operator">:</span> <span class="token punctuation">{</span>
        subscribe<span class="token operator">:</span> <span class="token punctuation">(</span><span class="token function-variable function">listener</span><span class="token operator">:</span> <span class="token punctuation">(</span>data<span class="token operator">:</span> ScannerDataEvent<span class="token punctuation">)</span> <span class="token operator">=&gt;</span> <span class="token keyword keyword-void">void</span><span class="token punctuation">)</span><span class="token operator">:</span> <span class="token punctuation">(</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token operator">=&gt;</span> <span class="token keyword keyword-void">void</span><span class="token punctuation">)</span> <span class="token operator">=&gt;</span> <span class="token punctuation">{</span>
          <span class="token keyword keyword-return">return</span> bridge<span class="token punctuation">.</span><span class="token function">subscribe</span><span class="token punctuation">(</span><span class="token string">'scanner.dataReceived'</span><span class="token punctuation">,</span> listener<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token punctuation">}</span><span class="token punctuation">,</span>
      <span class="token punctuation">}</span><span class="token punctuation">,</span>
    <span class="token punctuation">}</span><span class="token punctuation">,</span>
  <span class="token punctuation">}</span><span class="token punctuation">,</span>
<span class="token punctuation">}</span><span class="token punctuation">;</span>
</code></pre><h3 id="54-使用方法-usage-example">5.4. 使用方法 (Usage Example) </h3>
<p>在 Vue 组件或其他业务逻辑中，只需导入 <code>nativeApi</code> 即可享受类型安全带来的所有好处。</p>
<pre data-role="codeBlock" data-info="typescript" class="language-typescript typescript"><code><span class="token comment">// file: src/views/SomeComponent.vue</span>

<span class="token keyword keyword-import">import</span> <span class="token punctuation">{</span> nativeApi <span class="token punctuation">}</span> <span class="token keyword keyword-from">from</span> <span class="token string">"@/services/nativeApi"</span><span class="token punctuation">;</span>
<span class="token keyword keyword-import">import</span> <span class="token punctuation">{</span> onUnmounted <span class="token punctuation">}</span> <span class="token keyword keyword-from">from</span> <span class="token string">"vue"</span><span class="token punctuation">;</span>

<span class="token comment">// --- 示例1: 调用并等待返回 ---</span>
<span class="token keyword keyword-async">async</span> <span class="token keyword keyword-function">function</span> <span class="token function">handlePrint</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
  <span class="token keyword keyword-try">try</span> <span class="token punctuation">{</span>
    <span class="token comment">// 直接调用方法，参数类型会被自动补全和检查</span>
    <span class="token keyword keyword-const">const</span> response <span class="token operator">=</span> <span class="token keyword keyword-await">await</span> nativeApi<span class="token punctuation">.</span>printer<span class="token punctuation">.</span><span class="token function">print</span><span class="token punctuation">(</span><span class="token punctuation">{</span> content<span class="token operator">:</span> <span class="token punctuation">{</span> ticket<span class="token operator">:</span> <span class="token string">"..."</span> <span class="token punctuation">}</span> <span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    
    <span class="token comment">// `response` 的类型被自动推断为: ApiResponse&lt;{ jobId: string }&gt;</span>
    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>response<span class="token punctuation">.</span>success<span class="token punctuation">)</span> <span class="token punctuation">{</span>
      <span class="token comment">// `response.data` 的类型是: { jobId: string }</span>
      <span class="token builtin">console</span><span class="token punctuation">.</span><span class="token function">log</span><span class="token punctuation">(</span><span class="token string">"打印成功，任务ID:"</span><span class="token punctuation">,</span> response<span class="token punctuation">.</span>data<span class="token punctuation">.</span>jobId<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span> <span class="token keyword keyword-else">else</span> <span class="token punctuation">{</span>
      <span class="token comment">// `response.code` 和 `response.message` 存在</span>
      <span class="token builtin">console</span><span class="token punctuation">.</span><span class="token function">error</span><span class="token punctuation">(</span><span class="token template-string"><span class="token template-punctuation string">`</span><span class="token string">打印业务失败 [</span><span class="token interpolation"><span class="token interpolation-punctuation punctuation">${</span>response<span class="token punctuation">.</span>code<span class="token interpolation-punctuation punctuation">}</span></span><span class="token string">]:</span><span class="token template-punctuation string">`</span></span><span class="token punctuation">,</span> response<span class="token punctuation">.</span>message<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>
  <span class="token punctuation">}</span> <span class="token keyword keyword-catch">catch</span> <span class="token punctuation">(</span>error<span class="token punctuation">)</span> <span class="token punctuation">{</span>
    <span class="token comment">// `error` 是 JsonRpcErrorResponse['error']，通常是通信层错误（如超时）</span>
    <span class="token builtin">console</span><span class="token punctuation">.</span><span class="token function">error</span><span class="token punctuation">(</span><span class="token string">"打印RPC调用失败:"</span><span class="token punctuation">,</span> error<span class="token punctuation">)</span><span class="token punctuation">;</span>
  <span class="token punctuation">}</span>
<span class="token punctuation">}</span>

<span class="token comment">// --- 示例2: 发送通知 ---</span>
nativeApi<span class="token punctuation">.</span>logger<span class="token punctuation">.</span><span class="token function">log</span><span class="token punctuation">(</span><span class="token punctuation">{</span> level<span class="token operator">:</span> <span class="token string">"info"</span><span class="token punctuation">,</span> message<span class="token operator">:</span> <span class="token string">"User opened print dialog"</span> <span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token comment">// --- 示例3: 订阅事件 ---</span>
<span class="token comment">// `params` 的类型被自动推断为: ScannerDataEvent</span>
<span class="token keyword keyword-const">const</span> unsubscribeScanner <span class="token operator">=</span> nativeApi<span class="token punctuation">.</span>events<span class="token punctuation">.</span>scanner<span class="token punctuation">.</span>dataReceived<span class="token punctuation">.</span><span class="token function">subscribe</span><span class="token punctuation">(</span><span class="token punctuation">(</span>params<span class="token punctuation">)</span> <span class="token operator">=&gt;</span> <span class="token punctuation">{</span>
  <span class="token builtin">console</span><span class="token punctuation">.</span><span class="token function">log</span><span class="token punctuation">(</span><span class="token string">"收到扫码枪数据:"</span><span class="token punctuation">,</span> params<span class="token punctuation">.</span>code<span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token comment">// 组件卸载时，务必取消订阅以防止内存泄漏</span>
<span class="token function">onUnmounted</span><span class="token punctuation">(</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token operator">=&gt;</span> <span class="token punctuation">{</span>
  <span class="token function">unsubscribeScanner</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
</code></pre><h3 id="55-安全考量-security-considerations">5.5. 安全考量 (Security Considerations) </h3>
<p>为了确保<code>JSBridge</code>的通信安全，防止潜在的恶意脚本攻击，必须在 <strong>原生层</strong> 实施严格的安全策略：</p>
<ol>
<li>
<p><strong>方法白名单 (Method Whitelist)</strong>:</p>
<ul>
<li><strong>描述</strong>: 原生（C#/Java）层应维护一个明确的、可被 Web 调用的方法列表（白名单）。</li>
<li><strong>实现</strong>: 当接收到来自 Web 的 RPC 请求时，首先检查其 <code>method</code> 字段是否位于白名单内。如果不在，应立即拒绝该请求并记录安全警告，而不是尝试执行未知方法。</li>
<li><strong>理由</strong>: 这是最核心的安全机制，从根本上杜绝了攻击者调用未授权或危险的原生 API 的可能性。</li>
</ul>
</li>
<li>
<p><strong>参数强校验 (Strong Parameter Validation)</strong>:</p>
<ul>
<li><strong>描述</strong>: 对于白名单中的每一个方法，原生层都必须对其接收到的 <code>params</code> 进行严格的校验。</li>
<li><strong>实现</strong>: 校验应包括类型、格式、范围和存在性检查。</li>
<li><strong>理由</strong>: 防止格式错误的、恶意的或越界的参数导致原生代码抛出未处理的异常，甚至引发更严重的安全问题（如路径遍历、SQL注入等）。</li>
</ul>
</li>
<li>
<p><strong>来源验证 (Origin Validation)</strong>:</p>
<ul>
<li><strong>描述</strong>: 在 WebView 初始化时，验证其加载的内容来源是否可信。</li>
<li><strong>实现</strong>: 仅允许加载打包在应用内的本地 <code>html</code> 文件，或限定加载指定的、受信任的远程 HTTPS 域。在 C# 中，可以通过监听 <code>CoreWebView2.NavigationStarting</code> 事件并检查 <code>args.Uri</code> 属性来实现。</li>
<li><strong>理由</strong>: 防止应用被引导去加载一个恶意的第三方网页，该网页中的脚本可能会尝试利用 JSBridge 攻击原生应用。</li>
</ul>
</li>
</ol>
<h2 id="6-响应式布局方案">6. 响应式布局方案 </h2>
<h3 id="61-多分辨率适配策略">6.1. 多分辨率适配策略 </h3>
<h4 id="611-css-变量动态缩放">6.1.1. CSS 变量动态缩放 </h4>
<pre data-role="codeBlock" data-info="scss" class="language-scss scss"><code><span class="token comment">// styles/responsive.scss</span>
<span class="token selector">:root </span><span class="token punctuation">{</span>
  <span class="token comment">// 基础尺寸</span>
  <span class="token property">--base-font-size</span><span class="token punctuation">:</span> <span class="token number">16</span><span class="token unit">px</span><span class="token punctuation">;</span>
  <span class="token property">--base-spacing</span><span class="token punctuation">:</span> <span class="token number">20</span><span class="token unit">px</span><span class="token punctuation">;</span>
  <span class="token property">--base-button-height</span><span class="token punctuation">:</span> <span class="token number">48</span><span class="token unit">px</span><span class="token punctuation">;</span>
  <span class="token property">--base-input-height</span><span class="token punctuation">:</span> <span class="token number">40</span><span class="token unit">px</span><span class="token punctuation">;</span>

  <span class="token comment">// 缩放后的尺寸</span>
  <span class="token property">--font-size</span><span class="token punctuation">:</span> <span class="token function">calc</span><span class="token punctuation">(</span><span class="token function">var</span><span class="token punctuation">(</span>--base-font-size<span class="token punctuation">)</span> <span class="token operator">*</span> <span class="token function">var</span><span class="token punctuation">(</span>--scale<span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
  <span class="token property">--spacing</span><span class="token punctuation">:</span> <span class="token function">calc</span><span class="token punctuation">(</span><span class="token function">var</span><span class="token punctuation">(</span>--base-spacing<span class="token punctuation">)</span> <span class="token operator">*</span> <span class="token function">var</span><span class="token punctuation">(</span>--scale<span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
  <span class="token property">--button-height</span><span class="token punctuation">:</span> <span class="token function">calc</span><span class="token punctuation">(</span><span class="token function">var</span><span class="token punctuation">(</span>--base-button-height<span class="token punctuation">)</span> <span class="token operator">*</span> <span class="token function">var</span><span class="token punctuation">(</span>--scale<span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
  <span class="token property">--input-height</span><span class="token punctuation">:</span> <span class="token function">calc</span><span class="token punctuation">(</span><span class="token function">var</span><span class="token punctuation">(</span>--base-input-height<span class="token punctuation">)</span> <span class="token operator">*</span> <span class="token function">var</span><span class="token punctuation">(</span>--scale<span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

  <span class="token comment">// 断点定义</span>
  <span class="token property">--breakpoint-sm</span><span class="token punctuation">:</span> <span class="token number">1024</span><span class="token unit">px</span><span class="token punctuation">;</span>
  <span class="token property">--breakpoint-md</span><span class="token punctuation">:</span> <span class="token number">1366</span><span class="token unit">px</span><span class="token punctuation">;</span>
  <span class="token property">--breakpoint-lg</span><span class="token punctuation">:</span> <span class="token number">1920</span><span class="token unit">px</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span>

<span class="token comment">// 应用缩放</span>
<span class="token selector">.responsive-container </span><span class="token punctuation">{</span>
  <span class="token property">font-size</span><span class="token punctuation">:</span> <span class="token function">var</span><span class="token punctuation">(</span>--font-size<span class="token punctuation">)</span><span class="token punctuation">;</span>

  <span class="token selector">.button </span><span class="token punctuation">{</span>
    <span class="token property">height</span><span class="token punctuation">:</span> <span class="token function">var</span><span class="token punctuation">(</span>--button-height<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token property">padding</span><span class="token punctuation">:</span> <span class="token function">calc</span><span class="token punctuation">(</span><span class="token function">var</span><span class="token punctuation">(</span>--spacing<span class="token punctuation">)</span> <span class="token operator">/</span> <span class="token number">2</span><span class="token punctuation">)</span> <span class="token function">var</span><span class="token punctuation">(</span>--spacing<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token property">border-radius</span><span class="token punctuation">:</span> <span class="token function">calc</span><span class="token punctuation">(</span><span class="token function">var</span><span class="token punctuation">(</span>--spacing<span class="token punctuation">)</span> <span class="token operator">/</span> <span class="token number">4</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
  <span class="token punctuation">}</span>

  <span class="token selector">.input </span><span class="token punctuation">{</span>
    <span class="token property">height</span><span class="token punctuation">:</span> <span class="token function">var</span><span class="token punctuation">(</span>--input-height<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token property">padding</span><span class="token punctuation">:</span> <span class="token number">0</span> <span class="token function">calc</span><span class="token punctuation">(</span><span class="token function">var</span><span class="token punctuation">(</span>--spacing<span class="token punctuation">)</span> <span class="token operator">/</span> <span class="token number">2</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
  <span class="token punctuation">}</span>

  <span class="token selector">.spacing </span><span class="token punctuation">{</span>
    <span class="token property">margin</span><span class="token punctuation">:</span> <span class="token function">var</span><span class="token punctuation">(</span>--spacing<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token property">padding</span><span class="token punctuation">:</span> <span class="token function">var</span><span class="token punctuation">(</span>--spacing<span class="token punctuation">)</span><span class="token punctuation">;</span>
  <span class="token punctuation">}</span>
<span class="token punctuation">}</span>
</code></pre><h4 id="612-vue-组合式函数屏幕适配">6.1.2. Vue 组合式函数：屏幕适配 </h4>
<pre data-role="codeBlock" data-info="typescript" class="language-typescript typescript"><code><span class="token comment">// composables/useResponsive.ts</span>
<span class="token keyword keyword-import">import</span> <span class="token punctuation">{</span> ref<span class="token punctuation">,</span> onMounted<span class="token punctuation">,</span> onUnmounted<span class="token punctuation">,</span> computed <span class="token punctuation">}</span> <span class="token keyword keyword-from">from</span> <span class="token string">"vue"</span><span class="token punctuation">;</span>

<span class="token keyword keyword-export">export</span> <span class="token keyword keyword-function">function</span> <span class="token function">useResponsive</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
  <span class="token keyword keyword-const">const</span> screenWidth <span class="token operator">=</span> <span class="token function">ref</span><span class="token punctuation">(</span>window<span class="token punctuation">.</span>innerWidth<span class="token punctuation">)</span><span class="token punctuation">;</span>
  <span class="token keyword keyword-const">const</span> screenHeight <span class="token operator">=</span> <span class="token function">ref</span><span class="token punctuation">(</span>window<span class="token punctuation">.</span>innerHeight<span class="token punctuation">)</span><span class="token punctuation">;</span>
  <span class="token keyword keyword-const">const</span> scale <span class="token operator">=</span> <span class="token function">ref</span><span class="token punctuation">(</span><span class="token number">1</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

  <span class="token comment">// 屏幕类型</span>
  <span class="token keyword keyword-const">const</span> screenType <span class="token operator">=</span> <span class="token function">computed</span><span class="token punctuation">(</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token operator">=&gt;</span> <span class="token punctuation">{</span>
    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>screenWidth<span class="token punctuation">.</span>value <span class="token operator">&lt;=</span> <span class="token number">1024</span><span class="token punctuation">)</span> <span class="token keyword keyword-return">return</span> <span class="token string">"small"</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>screenWidth<span class="token punctuation">.</span>value <span class="token operator">&lt;=</span> <span class="token number">1366</span><span class="token punctuation">)</span> <span class="token keyword keyword-return">return</span> <span class="token string">"medium"</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>screenWidth<span class="token punctuation">.</span>value <span class="token operator">&lt;=</span> <span class="token number">1920</span><span class="token punctuation">)</span> <span class="token keyword keyword-return">return</span> <span class="token string">"large"</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-return">return</span> <span class="token string">"xlarge"</span><span class="token punctuation">;</span>
  <span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

  <span class="token comment">// 是否为竖屏</span>
  <span class="token keyword keyword-const">const</span> isPortrait <span class="token operator">=</span> <span class="token function">computed</span><span class="token punctuation">(</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token operator">=&gt;</span> screenHeight<span class="token punctuation">.</span>value <span class="token operator">&gt;</span> screenWidth<span class="token punctuation">.</span>value<span class="token punctuation">)</span><span class="token punctuation">;</span>

  <span class="token comment">// 计算缩放比例</span>
  <span class="token keyword keyword-const">const</span> <span class="token function-variable function">calculateScale</span> <span class="token operator">=</span> <span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token operator">=&gt;</span> <span class="token punctuation">{</span>
    <span class="token keyword keyword-const">const</span> baseWidth <span class="token operator">=</span> <span class="token number">1920</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-const">const</span> baseHeight <span class="token operator">=</span> <span class="token number">1080</span><span class="token punctuation">;</span>

    <span class="token keyword keyword-const">const</span> scaleX <span class="token operator">=</span> screenWidth<span class="token punctuation">.</span>value <span class="token operator">/</span> baseWidth<span class="token punctuation">;</span>
    <span class="token keyword keyword-const">const</span> scaleY <span class="token operator">=</span> screenHeight<span class="token punctuation">.</span>value <span class="token operator">/</span> baseHeight<span class="token punctuation">;</span>

    scale<span class="token punctuation">.</span>value <span class="token operator">=</span> Math<span class="token punctuation">.</span><span class="token function">min</span><span class="token punctuation">(</span>scaleX<span class="token punctuation">,</span> scaleY<span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token comment">// 设置CSS变量</span>
    document<span class="token punctuation">.</span>documentElement<span class="token punctuation">.</span>style<span class="token punctuation">.</span><span class="token function">setProperty</span><span class="token punctuation">(</span>
      <span class="token string">"--scale"</span><span class="token punctuation">,</span>
      scale<span class="token punctuation">.</span>value<span class="token punctuation">.</span><span class="token function">toString</span><span class="token punctuation">(</span><span class="token punctuation">)</span>
    <span class="token punctuation">)</span><span class="token punctuation">;</span>
  <span class="token punctuation">}</span><span class="token punctuation">;</span>

  <span class="token comment">// 响应窗口大小变化</span>
  <span class="token keyword keyword-const">const</span> <span class="token function-variable function">handleResize</span> <span class="token operator">=</span> <span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token operator">=&gt;</span> <span class="token punctuation">{</span>
    screenWidth<span class="token punctuation">.</span>value <span class="token operator">=</span> window<span class="token punctuation">.</span>innerWidth<span class="token punctuation">;</span>
    screenHeight<span class="token punctuation">.</span>value <span class="token operator">=</span> window<span class="token punctuation">.</span>innerHeight<span class="token punctuation">;</span>
    <span class="token function">calculateScale</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
  <span class="token punctuation">}</span><span class="token punctuation">;</span>

  <span class="token comment">// 获取响应式尺寸</span>
  <span class="token keyword keyword-const">const</span> getResponsiveSize <span class="token operator">=</span> <span class="token punctuation">(</span>baseSize<span class="token operator">:</span> <span class="token builtin">number</span><span class="token punctuation">)</span><span class="token operator">:</span> <span class="token builtin">number</span> <span class="token operator">=&gt;</span> <span class="token punctuation">{</span>
    <span class="token keyword keyword-return">return</span> Math<span class="token punctuation">.</span><span class="token function">round</span><span class="token punctuation">(</span>baseSize <span class="token operator">*</span> scale<span class="token punctuation">.</span>value<span class="token punctuation">)</span><span class="token punctuation">;</span>
  <span class="token punctuation">}</span><span class="token punctuation">;</span>

  <span class="token comment">// 获取响应式样式</span>
  <span class="token keyword keyword-const">const</span> <span class="token function-variable function">getResponsiveStyle</span> <span class="token operator">=</span> <span class="token punctuation">(</span>styles<span class="token operator">:</span> Record<span class="token operator">&lt;</span><span class="token builtin">string</span><span class="token punctuation">,</span> <span class="token builtin">number</span><span class="token operator">&gt;</span><span class="token punctuation">)</span> <span class="token operator">=&gt;</span> <span class="token punctuation">{</span>
    <span class="token keyword keyword-const">const</span> result<span class="token operator">:</span> Record<span class="token operator">&lt;</span><span class="token builtin">string</span><span class="token punctuation">,</span> <span class="token builtin">string</span><span class="token operator">&gt;</span> <span class="token operator">=</span> <span class="token punctuation">{</span><span class="token punctuation">}</span><span class="token punctuation">;</span>

    <span class="token keyword keyword-for">for</span> <span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> <span class="token punctuation">[</span>key<span class="token punctuation">,</span> value<span class="token punctuation">]</span> <span class="token keyword keyword-of">of</span> Object<span class="token punctuation">.</span><span class="token function">entries</span><span class="token punctuation">(</span>styles<span class="token punctuation">)</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
      result<span class="token punctuation">[</span>key<span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token template-string"><span class="token template-punctuation string">`</span><span class="token interpolation"><span class="token interpolation-punctuation punctuation">${</span><span class="token function">getResponsiveSize</span><span class="token punctuation">(</span>value<span class="token punctuation">)</span><span class="token interpolation-punctuation punctuation">}</span></span><span class="token string">px</span><span class="token template-punctuation string">`</span></span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>

    <span class="token keyword keyword-return">return</span> result<span class="token punctuation">;</span>
  <span class="token punctuation">}</span><span class="token punctuation">;</span>

  <span class="token function">onMounted</span><span class="token punctuation">(</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token operator">=&gt;</span> <span class="token punctuation">{</span>
    <span class="token function">calculateScale</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    window<span class="token punctuation">.</span><span class="token function">addEventListener</span><span class="token punctuation">(</span><span class="token string">"resize"</span><span class="token punctuation">,</span> handleResize<span class="token punctuation">)</span><span class="token punctuation">;</span>
  <span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

  <span class="token function">onUnmounted</span><span class="token punctuation">(</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token operator">=&gt;</span> <span class="token punctuation">{</span>
    window<span class="token punctuation">.</span><span class="token function">removeEventListener</span><span class="token punctuation">(</span><span class="token string">"resize"</span><span class="token punctuation">,</span> handleResize<span class="token punctuation">)</span><span class="token punctuation">;</span>
  <span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

  <span class="token keyword keyword-return">return</span> <span class="token punctuation">{</span>
    screenWidth<span class="token punctuation">,</span>
    screenHeight<span class="token punctuation">,</span>
    screenType<span class="token punctuation">,</span>
    isPortrait<span class="token punctuation">,</span>
    scale<span class="token punctuation">,</span>
    getResponsiveSize<span class="token punctuation">,</span>
    getResponsiveStyle
  <span class="token punctuation">}</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span>
</code></pre><h2 id="7-自动更新设计-auto-update-design">7. 自动更新设计 (Auto-Update Design) </h2>
<p>为了实现灵活、可控的软件版本迭代，自动更新机制将支持全量、灰度两种发布模式。该设计旨在确保更新过程的稳定性、安全性与用户体验。</p>
<h3 id="71-核心目标">7.1. 核心目标 </h3>
<ul>
<li><strong>可控发布</strong>: 管理员能够精确控制更新范围，支持设备ID或用户百分比进行灰度发布。</li>
<li><strong>无缝体验</strong>: 更新过程在后台自动进行，对用户的干扰降至最低。</li>
<li><strong>安全可靠</strong>: 所有更新包都需经过签名验证，防止恶意篡改；更新失败时应有回滚机制。</li>
<li><strong>前后端分离</strong>: 原生应用（C#）和Web前端（Vue）可独立或捆绑更新。</li>
</ul>
<h3 id="72-更新架构">7.2. 更新架构 </h3>
<p>更新系统由 <strong>更新服务器</strong> 和 <strong>客户端更新模块</strong> 两部分组成。</p>
<ul>
<li><strong>更新服务器</strong>:
<ul>
<li>存储所有版本的更新包（全量包与增量包）。</li>
<li>提供API供客户端检查更新。</li>
<li>托管更新策略配置文件，定义发布规则。</li>
</ul>
</li>
<li><strong>客户端更新模块 (集成在C#原生应用中)</strong>:
<ul>
<li>定期向更新服务器发起检查请求。</li>
<li>上报自身标识信息（设备ID、当前版本号）。</li>
<li>根据服务器返回的策略，下载并应用更新。</li>
<li>负责更新原生应用及内置的Web资源。</li>
</ul>
</li>
</ul>
<h3 id="73-更新策略配置">7.3. 更新策略配置 </h3>
<p>更新策略由部署在服务器的 <code>update-policy.json</code> 文件定义，它允许管理员灵活配置发布规则。</p>
<h4 id="731-配置文件结构">7.3.1. 配置文件结构 </h4>
<pre data-role="codeBlock" data-info="json" class="language-json json"><code><span class="token punctuation">{</span>
  <span class="token property">"schemaVersion"</span><span class="token operator">:</span> <span class="token string">"1.0"</span><span class="token punctuation">,</span>
  <span class="token property">"releases"</span><span class="token operator">:</span> <span class="token punctuation">[</span>
    <span class="token punctuation">{</span>
      <span class="token property">"version"</span><span class="token operator">:</span> <span class="token string">"2.1.0"</span><span class="token punctuation">,</span>
      <span class="token property">"releaseDate"</span><span class="token operator">:</span> <span class="token string">"2023-10-27T10:00:00Z"</span><span class="token punctuation">,</span>
      <span class="token property">"notes"</span><span class="token operator">:</span> <span class="token string">"- 新增会员积分功能\n- 优化订单结算速度"</span><span class="token punctuation">,</span>
      <span class="token property">"packages"</span><span class="token operator">:</span> <span class="token punctuation">{</span>
        <span class="token property">"native"</span><span class="token operator">:</span> <span class="token punctuation">{</span>
          <span class="token property">"url"</span><span class="token operator">:</span> <span class="token string">"https://update.server.com/v2.1.0/native.zip"</span><span class="token punctuation">,</span>
          <span class="token property">"sha256"</span><span class="token operator">:</span> <span class="token string">"a1b2c3d4..."</span>
        <span class="token punctuation">}</span><span class="token punctuation">,</span>
        <span class="token property">"web"</span><span class="token operator">:</span> <span class="token punctuation">{</span>
          <span class="token property">"url"</span><span class="token operator">:</span> <span class="token string">"https://update.server.com/v2.1.0/web.zip"</span><span class="token punctuation">,</span>
          <span class="token property">"sha256"</span><span class="token operator">:</span> <span class="token string">"e5f6g7h8..."</span>
        <span class="token punctuation">}</span>
      <span class="token punctuation">}</span><span class="token punctuation">,</span>
      <span class="token property">"strategy"</span><span class="token operator">:</span> <span class="token punctuation">{</span>
        <span class="token property">"type"</span><span class="token operator">:</span> <span class="token string">"grayscale"</span><span class="token punctuation">,</span> <span class="token comment">// "full" 或 "grayscale"</span>
        <span class="token property">"rules"</span><span class="token operator">:</span> <span class="token punctuation">[</span>
          <span class="token punctuation">{</span> <span class="token property">"by"</span><span class="token operator">:</span> <span class="token string">"deviceId"</span><span class="token punctuation">,</span> <span class="token property">"values"</span><span class="token operator">:</span> <span class="token punctuation">[</span><span class="token string">"D0-A1-B2-C3-D4-E5"</span><span class="token punctuation">]</span> <span class="token punctuation">}</span><span class="token punctuation">,</span>
          <span class="token punctuation">{</span> <span class="token property">"by"</span><span class="token operator">:</span> <span class="token string">"percentage"</span><span class="token punctuation">,</span> <span class="token property">"value"</span><span class="token operator">:</span> <span class="token number">10</span> <span class="token punctuation">}</span>
        <span class="token punctuation">]</span>
      <span class="token punctuation">}</span>
    <span class="token punctuation">}</span><span class="token punctuation">,</span>
    <span class="token punctuation">{</span>
      <span class="token property">"version"</span><span class="token operator">:</span> <span class="token string">"2.0.0"</span><span class="token punctuation">,</span>
      <span class="token property">"releaseDate"</span><span class="token operator">:</span> <span class="token string">"2023-10-20T18:00:00Z"</span><span class="token punctuation">,</span>
      <span class="token property">"notes"</span><span class="token operator">:</span> <span class="token string">"- 全新的UI设计\n- 支持多种支付方式"</span><span class="token punctuation">,</span>
      <span class="token property">"packages"</span><span class="token operator">:</span> <span class="token punctuation">{</span> <span class="token comment">/* ... */</span> <span class="token punctuation">}</span><span class="token punctuation">,</span>
      <span class="token property">"strategy"</span><span class="token operator">:</span> <span class="token punctuation">{</span>
        <span class="token property">"type"</span><span class="token operator">:</span> <span class="token string">"full"</span>
      <span class="token punctuation">}</span>
    <span class="token punctuation">}</span>
  <span class="token punctuation">]</span>
<span class="token punctuation">}</span>
</code></pre><h4 id="732-规则详解">7.3.2. 规则详解 </h4>
<ul>
<li><strong><code>releases</code></strong>: 一个数组，包含所有可用的发行版本，客户端将从上到下匹配。</li>
<li><strong><code>version</code></strong>: 遵循语义化版本号（<code>Major.Minor.Patch</code>）。</li>
<li><strong><code>packages</code></strong>: 包含 <code>native</code> 和 <code>web</code> 两部分的更新包信息。
<ul>
<li><code>url</code>: 更新包下载地址。</li>
<li><code>sha256</code>: 文件哈希，用于校验完整性。</li>
</ul>
</li>
<li><strong><code>strategy.type</code></strong>:
<ul>
<li><code>full</code>: 全量更新，所有请求更新的客户端都会收到此版本。</li>
<li><code>grayscale</code>: 灰度更新，只有满足 <code>rules</code> 中至少一条规则的客户端才会收到此版本。</li>
</ul>
</li>
<li><strong><code>strategy.rules</code></strong> (仅在 <code>type</code> 为 <code>grayscale</code> 时有效):
<ul>
<li><code>by</code>: 规则类型，可以是 <code>deviceId</code> (设备唯一标识), 或 <code>percentage</code> (用户百分比)。</li>
<li><code>values</code>/<code>value</code>: 规则的值。对于 <code>percentage</code>，客户端可以通过 <code>hash(deviceId) % 100 &lt; value</code> 来判断是否命中。</li>
</ul>
</li>
</ul>
<h3 id="74-更新流程">7.4. 更新流程 </h3>
<div class="mermaid">sequenceDiagram
    participant Client as POS客户端 (C#)
    participant Server as 更新服务器

    Client-&gt;&gt;Server: 1. 检查更新请求 (携带 storeId, deviceId, currentVersion)
    Server-&gt;&gt;Server: 2. 加载 update-policy.json
    Server-&gt;&gt;Server: 3. 遍历 releases，匹配客户端版本和策略
    alt 找到匹配的更新
        Server--&gt;&gt;Client: 4a. 响应更新信息 (newVersion, notes, packageUrls, hashes)
        Client-&gt;&gt;Client: 5. 在后台下载更新包
        Client-&gt;&gt;Client: 6. 校验文件哈希 (SHA256)
        Client-&gt;&gt;Client: 7. 解压并替换旧文件 (失败则回滚)
        Client-&gt;&gt;Client: 8. 提示用户重启以应用更新
    else 未找到更新
        Server--&gt;&gt;Client: 4b. 响应无可用更新
    end
</div><h3 id="75-安全与回滚">7.5. 安全与回滚 </h3>
<ul>
<li><strong>签名验证</strong>: 所有下发的更新包（尤其是原生执行文件）必须经过代码签名。客户端在应用更新前必须验证签名，确保来源可信、未被篡改。</li>
<li><strong>原子操作与回滚</strong>: 更新过程应是原子的。推荐采用“蓝绿部署”模式：将新版本下载到新的目录，验证成功后，通过修改一个指向当前版本的符号链接或配置文件来切换版本。如果新版本启动失败，只需将指针指回旧版本即可快速回滚。</li>
</ul>
<h2 id="8-日志系统-logging-system">8. 日志系统 (Logging System) </h2>
<p>一个分层、可远程调试的日志系统至关重要。</p>
<ul>
<li>
<p><strong>原生日志核心 (C#):</strong></p>
<ul>
<li>使用 <code>Serilog</code> 或 <code>NLog</code> 框架。</li>
<li>配置多个输出目标（Sinks）：
<ul>
<li><strong>File Sink:</strong> 在本地按日期滚动记录所有级别的日志文件，便于排查。</li>
<li><strong>Remote Sink (可选):</strong> 将 <code>WARN</code> 和 <code>ERROR</code> 级别以上的日志实时上报到 Sentry、ELK 等远程日志分析平台。</li>
</ul>
</li>
<li>通过 JSBridge 暴露接口给 Web，用于接收并记录来自 Web 端的日志（支持单条和批量）。</li>
</ul>
</li>
<li>
<p><strong>Web 端日志 (Vue):</strong></p>
<ul>
<li>封装 <code>logger.ts</code> 服务，该服务负责日志的收集与上报。</li>
<li>通过 <code>app.config.errorHandler</code> 和 <code>window.addEventListener('unhandledrejection', ...)</code> 全局捕获 Vue 组件异常和 Promise 异常。</li>
<li><strong>上报策略</strong>:
<ul>
<li><strong>实时上报</strong>: <code>ERROR</code> 级别的严重错误（如未捕获的异常）会立即调用 <code>nativeApi.logger.log</code> 发送给原生层。</li>
<li><strong>批量上报</strong>: <code>INFO</code>、<code>WARN</code> 等普通级别的日志会先暂存在内存队列中。<strong>每小时</strong>通过定时器 (<code>setInterval</code>) 触发，或当日志数量达到阈值时，将队列中的日志打包，调用 <code>JSBridge</code> 接口一次性发送给原生层进行归档，发送成功后清空队列。</li>
</ul>
</li>
<li><strong>日志存储</strong>: 日志主要由原生层负责持久化，Web 端只做内存缓存，不直接操作文件系统或 <code>localStorage</code>，以避免性能开销和存储限制。</li>
</ul>
</li>
</ul>
<h2 id="9-用户行为分析-baidu-tongji">9. 用户行为分析 (Baidu Tongji) </h2>
<p>为了解用户行为，我们集成百度统计。</p>
<ol>
<li><strong>引入脚本:</strong> 在 <code>index.html</code> 中引入百度统计的基础 JS 代码。</li>
<li><strong>PV 上报:</strong> 在 <code>vue-router</code> 的全局后置守卫 <code>router.afterEach</code> 中，手动调用 <code>window._hmt.push(['_trackPageview', to.fullPath]);</code> 来上报页面浏览, 或开启单页应用。</li>
<li><strong>事件上报:</strong> 封装一个 <code>trackEvent(category, action, ...)</code> 工具函数，在关键的用户操作点（如点击支付、添加商品）进行调用，上报自定义事件。</li>
</ol>
<h2 id="10-项目结构设计-project-structure">10. 项目结构设计 (Project Structure) </h2>
<p>一个清晰、可扩展的项目结构是高效协作和长期维护的基石。我们采用功能模块化的方式组织代码，确保高内聚、低耦合。</p>
<h3 id="101-c-原生宿主-pos-native">10.1. C# 原生宿主 (<code>pos-native</code>) </h3>
<p>原生宿主负责提供稳定的运行环境、硬件访问能力和核心的非 UI 业务逻辑。</p>
<pre data-role="codeBlock" data-info="plaintext" class="language-plaintext plaintext"><code>/pos-native
|-- /assets                     # 应用图标、原生图片等资源
|-- /bridge                     # 通信桥接层
|   |-- BridgeHandler.cs        # 核心消息处理器，解析RPC请求并分发
|   |-- JsonRpcModels.cs        # JSON-RPC 2.0 协议的数据模型
|   `-- NativeApi.cs            # 暴露给Web的所有原生方法的实现
|-- /core                       # 核心业务逻辑
|   |-- /data                   # 数据访问层 (如果需要本地数据库)
|   `-- /services               # 核心服务，如订单处理、库存管理等
|-- /hardware                   # 硬件抽象层
|   |-- /drivers                # 具体硬件驱动的封装
|   |-- IPrinter.cs             # 打印机接口
|   |-- IScanner.cs             # 扫描枪接口
|   `-- PrinterService.cs       # 打印机服务实现
|-- /platform                   # 平台相关功能
|   |-- AutoUpdater.cs          # 自动更新服务
|   `-- WindowManager.cs        # 窗口管理
|-- /web-app                    # (构建产物) 存放Vue打包后的静态文件
|-- MainWindow.xaml             # 主窗口定义 (WPF)
|-- MainWindow.xaml.cs          # 主窗口后台代码，初始化WebView2
|-- App.xaml                    # 应用定义
|-- App.xaml.cs                 # 应用启动逻辑
|-- Program.cs                  # [可选] 控制台应用程序入口
`-- pos-native.csproj           # 项目文件
</code></pre><h3 id="102-vue-前端核心-pos-web">10.2. Vue 前端核心 (<code>pos-web</code>) </h3>
<p>Web 核心负责所有 UI 展现、用户交互和业务流程的编排。</p>
<pre data-role="codeBlock" data-info="plaintext" class="language-plaintext plaintext"><code>/pos-web
|-- /public                     # 静态资源，会被直接复制到dist目录
|   |-- favicon.ico
|   `-- index.html              # 应用入口HTML (可注入统计脚本)
|-- /src
|   |-- /api                    # 后端HTTP API请求封装 (axios实例、接口模块)
|   |-- /assets                 # 样式、图片、字体等项目资源
|   |   |-- /fonts
|   |   |-- /icons              # SVG图标
|   |   `-- /styles             # 全局样式、变量、混入 (main.scss, responsive.scss)
|   |-- /bridge                 # 与原生通信的桥接服务
|   |   |-- index.ts            # BridgeService的实现和导出
|   |   `-- types.ts            # JSON-RPC和事件的TypeScript类型定义
|   |-- /components             # 全局通用组件 (原子组件、业务组件)
|   |   |-- /common             # 基础UI组件 (Button.vue, Input.vue, Modal.vue)
|   |   `-- /business           # 业务相关组件 (ProductCard.vue, OrderList.vue)
|   |-- /composables            # Vue组合式函数 (Hooks)
|   |   |-- use-responsive.ts   # 响应式布局逻辑
|   |   `-- use-event-bus.ts    # 事件总线
|   |-- /constants              # 全局常量 (枚举、配置)
|   |-- /layouts                # 布局组件
|   |   |-- DefaultLayout.vue
|   |   `-- CashierLayout.vue
|   |-- /router                 # Vue Router配置
|   |   `-- index.ts
|   |-- /store                  # Pinia状态管理
|   |   |-- /modules            # 按模块拆分 (user.ts, cart.ts, settings.ts)
|   |   `-- index.ts            # 根store配置
|   |-- /types                  # 全局TypeScript类型定义
|   |   `-- index.d.ts
|   |-- /utils                  # 通用工具函数
|   |   |-- format.ts           # 格式化函数
|   |   `-- request.ts          # HTTP请求封装
|   |-- /views                  # 页面级组件 (路由组件)
|   |   |-- /cashier
|   |   |-- /dashboard
|   |   `-- /login
|   |-- App.vue                 # 根组件
|   `-- main.ts                 # 应用入口文件
|-- .editorconfig
|-- .env.development            # 开发环境变量
|-- .env.production             # 生产环境变量
|-- .eslintrc.cjs
|-- .gitignore
|-- package.json
|-- postcss.config.js
|-- tsconfig.json
`-- vite.config.ts
</code></pre><h2 id="11-开发规范与指南-development-guide">11. 开发规范与指南 (Development Guide) </h2>
<p>为确保代码质量、开发效率和长期可维护性，所有团队成员必须遵守以下规范。</p>
<h3 id="111-命名规范">11.1. 命名规范 </h3>
<ul>
<li><strong>目录/文件夹</strong>: 全部使用小写 <code>kebab-case</code> (短横线连接)。例如: <code>src/components/business</code>, <code>src/composables</code>。</li>
<li><strong>Vue 组件文件</strong>: 使用 <code>PascalCase</code> (大驼峰命名)。例如: <code>ProductCard.vue</code>, <code>BaseModal.vue</code>。</li>
<li><strong>非组件 TS/JS 文件</strong>: 使用 <code>kebab-case</code>。例如: <code>use-responsive.ts</code>, <code>api-client.ts</code>。</li>
<li><strong>C# 文件/类/接口</strong>: 使用 <code>PascalCase</code>。接口名前加 <code>I</code>。例如: <code>PrinterService.cs</code>, <code>IScanner.cs</code>。</li>
<li><strong>变量/函数</strong>: 使用 <code>camelCase</code> (小驼峰命名)。例如: <code>const cartItems = ...</code>, <code>function calculateTotal() {}</code>。</li>
<li><strong>CSS 类名</strong>: 使用 <code>kebab-case</code>，并可结合 BEM 思想。例如: <code>.product-card__title</code>。</li>
</ul>
<h3 id="112-git-工作流与提交规范">11.2. Git 工作流与提交规范 </h3>
<ul>
<li><strong>分支模型</strong>:
<ul>
<li><code>main</code>: 生产分支，只接受来自 <code>develop</code> 的合并。</li>
<li><code>develop</code>: 开发主分支，集成所有已完成的功能。</li>
<li><code>feature/xxx</code>: 功能开发分支，从 <code>develop</code> 创建，完成后合并回 <code>develop</code>。</li>
<li><code>fix/xxx</code>: Bug 修复分支。</li>
<li><code>refactor/xxx</code>: 重构分支。</li>
</ul>
</li>
<li><strong>提交信息 (Commit Message)</strong>: 遵循 <a href="https://alibaba.github.io/f2e-spec/zh/engineering/git/">Git 提交规范</a> 规范。
<ul>
<li>格式: <code>&lt;type&gt;(&lt;scope&gt;): &lt;subject&gt;</code></li>
<li>示例: <code>feat(cart): add item removal functionality</code></li>
<li>常用 <code>type</code>: <code>feat</code>, <code>fix</code>, <code>docs</code>, <code>style</code>, <code>refactor</code>, <code>test</code>, <code>chore</code>。</li>
</ul>
</li>
</ul>
<h3 id="113-编码规范">11.3. 编码规范 </h3>
<ul>
<li><strong>接口先行</strong>: 所有原生与 Web 的交互，必须先在 <code>src/bridge/types.ts</code> 中定义清晰的 TypeScript 接口和事件类型。</li>
<li><strong>职责边界</strong>: 严格遵守功能划分。原生只提供“能力”，Web 负责“展现”和“业务编排”。原生接口应是功能性的 (<code>print</code>) 而非过程性的 (<code>openPort</code>)。</li>
<li><strong>Vue 最佳实践</strong>:
<ul>
<li>优先使用 <code>&lt;script setup&gt;</code> 语法。</li>
<li>组合式函数 (<code>composables</code>) 应用于封装和复用有状态逻辑。</li>
<li>遵循单向数据流，UI 组件应从 Pinia Store 获取状态，通过 Action 修改状态。</li>
<li>组件 props 定义应尽可能详细，包含 <code>type</code>, <code>required</code>, <code>default</code> 和 <code>validator</code>。</li>
<li>Vue (核心) <a href="https://vuejs.org/style-guide/rules-essential.html">官方风格指南 (必要)</a></li>
<li>Vue (强烈推荐) <a href="https://vuejs.org/style-guide/rules-strongly-recommended.html">官方风格指南 (强烈推荐)</a></li>
<li>Vue (推荐) <a href="https://vuejs.org/style-guide/rules-recommended.html">官方风格指南 (推荐)</a></li>
<li>Vue (谨慎使用) <a href="https://vuejs.org/style-guide/rules-use-with-caution.html">官方风格指南 (谨慎使用)</a></li>
<li>TypeScript <a href="https://alibaba.github.io/f2e-spec/zh/coding/typescript/">阿里巴巴前端规约</a></li>
</ul>
</li>
<li><strong>C#最佳实践</strong>:
<ul>
<li>遵循 SOLID 原则。</li>
<li>使用依赖注入（Dependency Injection）来解耦服务。</li>
<li>异步方法必须使用 <code>async/await</code> 并以 <code>Async</code> 后缀命名。</li>
</ul>
</li>
<li><strong>日志规范</strong>: 关键用户路径、API 调用和错误处理必须记录日志。日志信息需包含充足的上下文（如用户 ID、订单号、错误堆栈）。</li>
</ul>
<h3 id="114-代码审查-code-review">11.4. 代码审查 (Code Review) </h3>
<ul>
<li>所有向 <code>develop</code> 和 <code>main</code> 分支的合并请求 (Merge Request / Pull Request) 都必须经过至少一位其他团队成员的审查。</li>
<li>审查重点: 代码风格、逻辑正确性、可读性、性能、是否符合架构设计。</li>
</ul>
<h3 id="115-拥抱未来">11.5. 拥抱未来 </h3>
<ul>
<li><code>bridge</code> 层中的接口设计应保持平台无关性，为未来可能的 Android 或 Web 端独立部署奠定基础。</li>
</ul>

      </div>
      
      
    
    
    <script type="module">
// TODO: If ZenUML gets integrated into mermaid in the future,
//      we can remove the following lines.


var MERMAID_CONFIG = ({"startOnLoad":false});
if (typeof MERMAID_CONFIG !== 'undefined') {
  MERMAID_CONFIG.startOnLoad = false
  MERMAID_CONFIG.cloneCssStyles = false
  MERMAID_CONFIG.theme = "default"
}

mermaid.initialize(MERMAID_CONFIG || {})
if (typeof(window['Reveal']) !== 'undefined') {
  function mermaidRevealHelper(event) {
    var currentSlide = event.currentSlide
    var diagrams = currentSlide.querySelectorAll('.mermaid')
    for (var i = 0; i < diagrams.length; i++) {
      var diagram = diagrams[i]
      if (!diagram.hasAttribute('data-processed')) {
        mermaid.init(null, diagram, ()=> {
          Reveal.slide(event.indexh, event.indexv)
        })
      }
    }
  }
  Reveal.addEventListener('slidetransitionend', mermaidRevealHelper)
  Reveal.addEventListener('ready', mermaidRevealHelper)
  await mermaid.run({
    nodes: document.querySelectorAll('.mermaid')
  })
} else {
  await mermaid.run({
    nodes: document.querySelectorAll('.mermaid')
  })
}
</script>
    
    
    
  
    </body></html>